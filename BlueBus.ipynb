{"cells": [{"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["**************\n"]}], "source": ["# print my current public IP address\n", "import requests\n", "\n", "def get_ip():\n", "    # return requests.get('http://ip.42.pl/raw').text\n", "    return requests.get('https://api.ipify.org').text\n", "print(get_ip())\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named '<PERSON><PERSON><PERSON><PERSON>'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[50], line 15\u001b[0m\n\u001b[0;32m      5\u001b[0m payload \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m      6\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124muser_name\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m01019403007\u001b[39m\u001b[38;5;124m'\u001b[39m, \n\u001b[0;32m      7\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpassword\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDz@FB5gTzC#vPn\u001b[39m\u001b[38;5;124m'\u001b[39m      \n\u001b[0;32m      8\u001b[0m }\n\u001b[0;32m     10\u001b[0m headers \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m     11\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mContent-Type\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mapplication/x-www-form-urlencoded\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[0;32m     12\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mUser-Agent\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mMozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     13\u001b[0m }\n\u001b[1;32m---> 15\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mcloudscraper\u001b[39;00m\n\u001b[0;32m     17\u001b[0m url \u001b[38;5;241m=\u001b[39m \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mhttps://api.bluebus.com.eg/api/agent/login\u001b[39m\u001b[38;5;124m'\u001b[39m\n\u001b[0;32m     18\u001b[0m payload \u001b[38;5;241m=\u001b[39m {\n\u001b[0;32m     19\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124muser_name\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124m01019403007\u001b[39m\u001b[38;5;124m'\u001b[39m, \n\u001b[0;32m     20\u001b[0m     \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mpassword\u001b[39m\u001b[38;5;124m'\u001b[39m: \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mDz@FB5gTzC#vPn\u001b[39m\u001b[38;5;124m'\u001b[39m      \n\u001b[0;32m     21\u001b[0m }\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'cloudscraper'"]}], "source": ["import requests\n", "\n", "url = 'https://api.bluebus.com.eg/api/agent/login'\n", "\n", "payload = {\n", "    'user_name': '01019403007', \n", "    'password': 'Dz@FB5gTzC#vPn'      \n", "}\n", "\n", "headers = {\n", "    'Content-Type': 'application/x-www-form-urlencoded',\n", "    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'\n", "}\n", "\n", "import cloudscraper\n", "\n", "url = 'https://api.bluebus.com.eg/api/agent/login'\n", "payload = {\n", "    'user_name': '01019403007', \n", "    'password': 'Dz@FB5gTzC#vPn'      \n", "}\n", "\n", "scraper = cloudscraper.create_scraper()\n", "\n", "response = scraper.post(url, data=payload)\n", "\n", "print(response.status_code, response.text)\n", "\n"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<Response [403]>\n"]}], "source": ["print(requests.post(url, json=payload, headers=headers))"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["\n", "access_token = \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FwaS5ibHVlYnVzLmNvbS5lZy9hcGkvYWdlbnQvbG9naW4iLCJpYXQiOjE3MzgxNDcxNTQsImV4cCI6NzczODE0NzE1NCwibmJmIjoxNzM4MTQ3MTU0LCJqdGkiOiJacjZ6ZW5VSGY2QjdyRjhiIiwic3ViIjo0MzUsInBydiI6Ijg3ZTBhZjFlZjlmZDE1ODEyZmRlYzk3MTUzYTE0ZTBiMDQ3NTQ2YWEiLCJwaG9uZSI6IjAxMDE5NDAzMDA3In0.qr4sVyMW9c0LYGzGlkUNQ0P1Mnsxt1gAmR7LNXzObvU\""]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL2FwaS5ibHVlYnVzLmNvbS5lZy9hcGkvYWdlbnQvbG9naW4iLCJpYXQiOjE3MzgxNDcxNTQsImV4cCI6NzczODE0NzE1NCwibmJmIjoxNzM4MTQ3MTU0LCJqdGkiOiJacjZ6ZW5VSGY2QjdyRjhiIiwic3ViIjo0MzUsInBydiI6Ijg3ZTBhZjFlZjlmZDE1ODEyZmRlYzk3MTUzYTE0ZTBiMDQ3NTQ2YWEiLCJwaG9uZSI6IjAxMDE5NDAzMDA3In0.qr4sVyMW9c0LYGzGlkUNQ0P1Mnsxt1gAmR7LNXzObvU\n"]}], "source": ["print(access_token)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Search failed with status code 403\n", "Response: <!DOCTYPE html>\n", "<html lang=\"en\">\n", "<head>\n", "    <meta charset=\"UTF-8\" />\n", "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n", "    <title>Error <PERSON></title>\n", "    <script src=\"https://kit.fontawesome.com/66aa7c98b3.js\" crossorigin=\"anonymous\"></script>\n", "    <style>\n", "        @import url(\"https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap\");\n", "        * { margin: 0; padding: 0; box-sizing: border-box; }\n", "        body { font-family: \"Poppins\", sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; }\n", "        .content { text-align: center; }\n", "        h1 { font-size: 2rem; font-weight: 700; }\n", "        p { font-size: 1rem; margin: 0.5rem 0; }\n", "        button { padding: 0.8rem; border-radius: 10px; border: none; background: #0046d4; color: #fff; font-size: 1rem; cursor: pointer; }\n", "    </style>\n", "</head>\n", "<body>\n", "    <div class=\"content\">\n", "        <img src=\"https://i.postimg.cc/2yrFyxKv/giphy.gif\" alt=\"gif\" style=\"width:100px; margin-bottom:1rem;\">\n", "        <h1>This page is gone.</h1>\n", "        <p>...maybe the page you're looking for is not found or never existed.</p>\n", "        <a href=\"https://www.google.com/search?q=bluebus.com.eg\" target=\"_blank\">\n", "            <button>Back to home <i class=\"far fa-hand-point-right\"></i></button>\n", "        </a>\n", "    </div>\n", "<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\"window.__CF$cv$params={r:'909899aaed645363',t:'MTczODE0NzE4Ni4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>\n", "</html>\n"]}], "source": ["import json\n", "# Assuming you have already retrieved accessToken from the login response\n", "headers = {\n", "    'Authorization': f'<PERSON><PERSON> {access_token}',\n", "    'Accept': 'application/json'\n", "}\n", "\n", "payload = {\n", "    'from_city_id': 15,\n", "    'to_city_id': 14,\n", "    'travel_date': '2024-11-22'\n", "}\n", "\n", "response = requests.post('https://api.bluebus.com.eg/api/agent/search-trip-by-city', headers=headers, data=payload)\n", "\n", "if response.status_code == 200:\n", "    print('Trip search successful')\n", "    \n", "    response_json = response.json()\n", "    print(json.dumps(response_json, indent=4, ensure_ascii=False))\n", "else:\n", "    print(f'Search failed with status code {response.status_code}')\n", "    print('Response:', response.text)\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["5\n"]}], "source": ["print(len(response_json['data']))\n", "trips = response_json['data']"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 475123, 'trip_id': 73780, 'location_id': 21, 'time': '11:00:00', 'date': '2024-11-22', 'created_at': '2024-10-02T11:13:08.000000Z', 'updated_at': '2024-10-02T11:13:08.000000Z', 'station': {'id': 21, 'name_en': 'El Ruwaysat', 'name_ar': 'الرويسات', 'lat': '27.898753', 'long': '34.286951', 'city_id': 14, 'city': {'id': 14, 'uuid': 'efb2da40-3d5a-4942-a474-2197cd1ccd9d', 'code': 'SSH', 'name_en': '<PERSON>har<PERSON> Sheikh', 'name_ar': 'شرم الشيخ', 'lat': '27.91222', 'long': '34.32972', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city141644292845.png', 'created_at': '2021-11-29T14:22:16.000000Z', 'updated_at': '2023-11-05T10:44:34.000000Z'}}}\n"]}], "source": ["print(trips[0]['trip_locations'][1])"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2024-10-05 22:00:00\n", "2024-10-05 01:00:00\n", "2024-10-05 23:00:00\n", "2024-10-05 00:30:00\n", "2024-10-05 02:30:00\n", "2024-10-05 01:00:00\n"]}], "source": ["for trip in trips:\n", "    print(trip['date'] , trip['time'])"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["id: 70779\n", "trip_template_id: 1720\n", "ref_code: 140C12-ALX-HRG\n", "access_level: 1\n", "date: 2024-10-11\n", "time: 00:30:00\n", "status: 3\n", "is_active: 1\n", "bus_salon_id: 2\n", "created_at: 2024-09-08T09:53:36.000000Z\n", "updated_at: 2024-09-08T09:54:32.000000Z\n", "deleted_at: None\n", "captured: None\n", "available_seats: {'unReservedSeat': [{'id': 1, 'name_en': 'Comfort', 'name_ar': 'كرسي مميز', 'rows': 1, 'cols': 1, 'facilities': [{'name': 'USB', 'icon': 'https://api.bluebus.com.eg/facilities/seats/USB.png'}], 'created_at': '2021-07-16T18:40:46.000000Z', 'updated_at': '2021-07-16T18:40:46.000000Z', 'deleted_at': None, 'count': 44, 'price': '330.00', 'seats_numbers': ['1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '11', '12', '13', '14', '15', '16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31', '32', '33', '34', '35', '36', '37', '38', '39', '40', '41', '42', '43', '44']}], 'status': True, 'message': ''}\n", "trip_locations: [{'id': 455774, 'trip_id': 70779, 'location_id': 53, 'time': '03:15:00', 'date': '2024-10-11', 'created_at': '2024-09-08T09:53:36.000000Z', 'updated_at': '2024-09-08T09:53:36.000000Z', 'station': {'id': 53, 'name_en': '<PERSON><PERSON> Emara<PERSON> Misr ', 'name_ar': 'دائرى المعادى امارات مصر', 'lat': '29.98611449092123', 'long': '31.308724150262492', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 455777, 'trip_id': 70779, 'location_id': 61, 'time': '08:00:00', 'date': '2024-10-11', 'created_at': '2024-09-08T09:53:36.000000Z', 'updated_at': '2024-09-08T09:53:36.000000Z', 'station': {'id': 61, 'name_en': 'Al Ahyaa', 'name_ar': 'الأحياء', 'lat': '27.297449871582913', 'long': '33.74555800129701', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}, {'id': 455778, 'trip_id': 70779, 'location_id': 24, 'time': '08:30:00', 'date': '2024-10-11', 'created_at': '2024-09-08T09:53:36.000000Z', 'updated_at': '2024-09-08T09:53:36.000000Z', 'station': {'id': 24, 'name_en': 'Watanya-HRG', 'name_ar': 'محطة وطنية', 'lat': '27.215725', 'long': '33.817452', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}, {'id': 455779, 'trip_id': 70779, 'location_id': 18, 'time': '09:00:00', 'date': '2024-10-11', 'created_at': '2024-09-08T09:53:36.000000Z', 'updated_at': '2024-09-08T09:53:36.000000Z', 'station': {'id': 18, 'name_en': 'El Nasr Street', 'name_ar': 'شارع النصر', 'lat': '27.241535', 'long': '33.828012', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}]\n", "route_lines: [{'id': 342120, 'trip_id': 70779, 'from_city_id': 15, 'to_city_id': 13, 'bus_id': None, 'created_at': '2024-09-08T09:53:36.000000Z', 'updated_at': '2024-09-08T09:53:36.000000Z', 'deleted_at': None, 'prices': [{'id': 454671, 'trip_route_line_id': 342120, 'seat_type_id': 1, 'price': '330.00', 'created_at': '2024-09-08T09:53:36.000000Z', 'updated_at': '2024-09-08T09:53:36.000000Z', 'discount_price': None, 'promo_id': None, 'seat_type': {'id': 1, 'name_en': 'Comfort', 'name_ar': 'كرسي مميز', 'rows': 1, 'cols': 1, 'facilities': [{'name': 'USB', 'icon': 'https://api.bluebus.com.eg/facilities/seats/USB.png'}], 'created_at': '2021-07-16T18:40:46.000000Z', 'updated_at': '2021-07-16T18:40:46.000000Z', 'deleted_at': None}}]}]\n", "bus_salon: {'id': 2, 'name': 'Comfort', 'layout': [[{'key': 1, 'cols': [0], 'rows': [0], 'type': 4, 'numbering': None}, {'key': 2, 'cols': [1], 'rows': [0], 'type': 5, 'numbering': None}, {'key': 3, 'cols': [2], 'rows': [0], 'type': 4, 'numbering': None}, {'key': 4, 'cols': [3], 'rows': [0], 'type': 5, 'numbering': None}, {'key': 5, 'cols': [4], 'rows': [0], 'type': 4, 'numbering': None}], [{'key': 6, 'cols': [0], 'rows': [1], 'type': 1, 'numbering': '1', 'seat_type_id': 1}, {'key': 7, 'cols': [1], 'rows': [1], 'type': 1, 'numbering': '2', 'seat_type_id': 1}, {'key': 8, 'cols': [2], 'rows': [1], 'type': 4, 'numbering': None}, {'key': 9, 'cols': [3], 'rows': [1], 'type': 1, 'numbering': '3', 'seat_type_id': 1}, {'key': 10, 'cols': [4], 'rows': [1], 'type': 1, 'numbering': '4', 'seat_type_id': 1}], [{'key': 11, 'cols': [0], 'rows': [2], 'type': 1, 'numbering': '5', 'seat_type_id': 1}, {'key': 12, 'cols': [1], 'rows': [2], 'type': 1, 'numbering': '6', 'seat_type_id': 1}, {'key': 13, 'cols': [2], 'rows': [2], 'type': 4, 'numbering': None}, {'key': 14, 'cols': [3], 'rows': [2], 'type': 1, 'numbering': '7', 'seat_type_id': 1}, {'key': 15, 'cols': [4], 'rows': [2], 'type': 1, 'numbering': '8', 'seat_type_id': 1}], [{'key': 16, 'cols': [0], 'rows': [3], 'type': 1, 'numbering': '9', 'seat_type_id': 1}, {'key': 17, 'cols': [1], 'rows': [3], 'type': 1, 'numbering': '10', 'seat_type_id': 1}, {'key': 18, 'cols': [2], 'rows': [3], 'type': 4, 'numbering': None}, {'key': 19, 'cols': [3], 'rows': [3], 'type': 1, 'numbering': '11', 'seat_type_id': 1}, {'key': 20, 'cols': [4], 'rows': [3], 'type': 1, 'numbering': '12', 'seat_type_id': 1}], [{'key': 21, 'cols': [0], 'rows': [4], 'type': 1, 'numbering': '13', 'seat_type_id': 1}, {'key': 22, 'cols': [1], 'rows': [4], 'type': 1, 'numbering': '14', 'seat_type_id': 1}, {'key': 23, 'cols': [2], 'rows': [4], 'type': 4, 'numbering': None}, {'key': 24, 'cols': [3], 'rows': [4], 'type': 1, 'numbering': '15', 'seat_type_id': 1}, {'key': 25, 'cols': [4], 'rows': [4], 'type': 1, 'numbering': '16', 'seat_type_id': 1}], [{'key': 26, 'cols': [0], 'rows': [5], 'type': 1, 'numbering': '17', 'seat_type_id': 1}, {'key': 27, 'cols': [1], 'rows': [5], 'type': 1, 'numbering': '18', 'seat_type_id': 1}, {'key': 28, 'cols': [2], 'rows': [5], 'type': 4, 'numbering': None}, {'key': 29, 'cols': [3], 'rows': [5], 'type': 4, 'numbering': None}, {'key': 30, 'cols': [4], 'rows': [5], 'type': 2, 'numbering': None}], [{'key': 31, 'cols': [0], 'rows': [6], 'type': 1, 'numbering': '19', 'seat_type_id': 1}, {'key': 32, 'cols': [1], 'rows': [6], 'type': 1, 'numbering': '20', 'seat_type_id': 1}, {'key': 33, 'cols': [2], 'rows': [6], 'type': 4, 'numbering': None}, {'key': 34, 'cols': [3], 'rows': [6], 'type': 4, 'numbering': None}, {'key': 35, 'cols': [4], 'rows': [6], 'type': 4, 'numbering': None}], [{'key': 36, 'cols': [0], 'rows': [7], 'type': 1, 'numbering': '21', 'seat_type_id': 1}, {'key': 37, 'cols': [1], 'rows': [7], 'type': 1, 'numbering': '22', 'seat_type_id': 1}, {'key': 38, 'cols': [2], 'rows': [7], 'type': 4, 'numbering': None}, {'key': 39, 'cols': [3], 'rows': [7], 'type': 1, 'numbering': '23', 'seat_type_id': 1}, {'key': 40, 'cols': [4], 'rows': [7], 'type': 1, 'numbering': '24', 'seat_type_id': 1}], [{'key': 41, 'cols': [0], 'rows': [8], 'type': 1, 'numbering': '25', 'seat_type_id': 1}, {'key': 42, 'cols': [1], 'rows': [8], 'type': 1, 'numbering': '26', 'seat_type_id': 1}, {'key': 43, 'cols': [2], 'rows': [8], 'type': 4, 'numbering': None}, {'key': 44, 'cols': [3], 'rows': [8], 'type': 1, 'numbering': '27', 'seat_type_id': 1}, {'key': 45, 'cols': [4], 'rows': [8], 'type': 1, 'numbering': '28', 'seat_type_id': 1}], [{'key': 46, 'cols': [0], 'rows': [9], 'type': 1, 'numbering': '29', 'seat_type_id': 1}, {'key': 47, 'cols': [1], 'rows': [9], 'type': 1, 'numbering': '30', 'seat_type_id': 1}, {'key': 48, 'cols': [2], 'rows': [9], 'type': 4, 'numbering': None}, {'key': 49, 'cols': [3], 'rows': [9], 'type': 1, 'numbering': '31', 'seat_type_id': 1}, {'key': 50, 'cols': [4], 'rows': [9], 'type': 1, 'numbering': '32', 'seat_type_id': 1}], [{'key': 51, 'cols': [0], 'rows': [10], 'type': 1, 'numbering': '33', 'seat_type_id': 1}, {'key': 52, 'cols': [1], 'rows': [10], 'type': 1, 'numbering': '34', 'seat_type_id': 1}, {'key': 53, 'cols': [2], 'rows': [10], 'type': 4, 'numbering': None}, {'key': 54, 'cols': [3], 'rows': [10], 'type': 1, 'numbering': '35', 'seat_type_id': 1}, {'key': 55, 'cols': [4], 'rows': [10], 'type': 1, 'numbering': '36', 'seat_type_id': 1}], [{'key': 56, 'cols': [0], 'rows': [11], 'type': 1, 'numbering': '37', 'seat_type_id': 1}, {'key': 57, 'cols': [1], 'rows': [11], 'type': 1, 'numbering': '38', 'seat_type_id': 1}, {'key': 58, 'cols': [2], 'rows': [11], 'type': 4, 'numbering': None}, {'key': 59, 'cols': [3], 'rows': [11], 'type': 1, 'numbering': '39', 'seat_type_id': 1}, {'key': 60, 'cols': [4], 'rows': [11], 'type': 1, 'numbering': '40', 'seat_type_id': 1}], [{'key': 61, 'cols': [0], 'rows': [12], 'type': 1, 'numbering': '41', 'seat_type_id': 1}, {'key': 62, 'cols': [1], 'rows': [12], 'type': 1, 'numbering': '42', 'seat_type_id': 1}, {'key': 63, 'cols': [2], 'rows': [12], 'type': 4, 'numbering': None}, {'key': 64, 'cols': [3], 'rows': [12], 'type': 1, 'numbering': '43', 'seat_type_id': 1}, {'key': 65, 'cols': [4], 'rows': [12], 'type': 1, 'numbering': '44', 'seat_type_id': 1}]], 'created_at': '2021-11-29T18:55:40.000000Z', 'updated_at': '2021-11-29T18:55:40.000000Z', 'display_name': 'Comfort'}\n"]}], "source": ["# print all attributes of the first trip\n", "for key, value in trips[0].items():\n", "    print(f'{key}: {value}')"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["id\n", "trip_template_id\n", "ref_code\n", "access_level\n", "date\n", "time\n", "status\n", "is_active\n", "bus_salon_id\n", "created_at\n", "updated_at\n", "deleted_at\n", "captured\n", "available_seats\n", "trip_locations\n", "route_lines\n", "bus_salon\n", "-------------------\n", "id\n", "trip_template_id\n", "ref_code\n", "access_level\n", "date\n", "time\n", "status\n", "is_active\n", "bus_salon_id\n", "created_at\n", "updated_at\n", "deleted_at\n", "captured\n", "available_seats\n", "trip_locations\n", "route_lines\n", "bus_salon\n", "-------------------\n", "id\n", "trip_template_id\n", "ref_code\n", "access_level\n", "date\n", "time\n", "status\n", "is_active\n", "bus_salon_id\n", "created_at\n", "updated_at\n", "deleted_at\n", "captured\n", "available_seats\n", "trip_locations\n", "route_lines\n", "bus_salon\n", "-------------------\n", "id\n", "trip_template_id\n", "ref_code\n", "access_level\n", "date\n", "time\n", "status\n", "is_active\n", "bus_salon_id\n", "created_at\n", "updated_at\n", "deleted_at\n", "captured\n", "available_seats\n", "trip_locations\n", "route_lines\n", "bus_salon\n", "-------------------\n", "id\n", "trip_template_id\n", "ref_code\n", "access_level\n", "date\n", "time\n", "status\n", "is_active\n", "bus_salon_id\n", "created_at\n", "updated_at\n", "deleted_at\n", "captured\n", "available_seats\n", "trip_locations\n", "route_lines\n", "bus_salon\n", "-------------------\n", "id\n", "trip_template_id\n", "ref_code\n", "access_level\n", "date\n", "time\n", "status\n", "is_active\n", "bus_salon_id\n", "created_at\n", "updated_at\n", "deleted_at\n", "captured\n", "available_seats\n", "trip_locations\n", "route_lines\n", "bus_salon\n", "-------------------\n"]}], "source": ["for trip in trips:\n", "    # print(trip['available_seats'])\n", "    for key, value in trip.items():\n", "        print(f'{key}')\n", "    print('-------------------')"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'id': 455774, 'trip_id': 70779, 'location_id': 53, 'time': '03:15:00', 'date': '2024-10-11', 'created_at': '2024-09-08T09:53:36.000000Z', 'updated_at': '2024-09-08T09:53:36.000000Z', 'station': {'id': 53, 'name_en': '<PERSON><PERSON> ', 'name_ar': 'دائرى المعادى امارات مصر', 'lat': '29.98611449092123', 'long': '31.308724150262492', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 455777, 'trip_id': 70779, 'location_id': 61, 'time': '08:00:00', 'date': '2024-10-11', 'created_at': '2024-09-08T09:53:36.000000Z', 'updated_at': '2024-09-08T09:53:36.000000Z', 'station': {'id': 61, 'name_en': 'Al Ahyaa', 'name_ar': 'الأحياء', 'lat': '27.297449871582913', 'long': '33.74555800129701', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}, {'id': 455778, 'trip_id': 70779, 'location_id': 24, 'time': '08:30:00', 'date': '2024-10-11', 'created_at': '2024-09-08T09:53:36.000000Z', 'updated_at': '2024-09-08T09:53:36.000000Z', 'station': {'id': 24, 'name_en': 'Watanya-HRG', 'name_ar': 'محطة وطنية', 'lat': '27.215725', 'long': '33.817452', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}, {'id': 455779, 'trip_id': 70779, 'location_id': 18, 'time': '09:00:00', 'date': '2024-10-11', 'created_at': '2024-09-08T09:53:36.000000Z', 'updated_at': '2024-09-08T09:53:36.000000Z', 'station': {'id': 18, 'name_en': 'El Nasr Street', 'name_ar': 'شارع النصر', 'lat': '27.241535', 'long': '33.828012', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}]\n", "[{'id': 455919, 'trip_id': 70810, 'location_id': 53, 'time': '05:30:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:27:32.000000Z', 'updated_at': '2024-09-08T10:27:32.000000Z', 'station': {'id': 53, 'name_en': '<PERSON><PERSON> Em<PERSON> Misr ', 'name_ar': 'دائرى المعادى امارات مصر', 'lat': '29.98611449092123', 'long': '31.308724150262492', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 455920, 'trip_id': 70810, 'location_id': 24, 'time': '10:30:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:27:32.000000Z', 'updated_at': '2024-09-08T10:27:32.000000Z', 'station': {'id': 24, 'name_en': 'Watanya-HRG', 'name_ar': 'محطة وطنية', 'lat': '27.215725', 'long': '33.817452', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}, {'id': 455921, 'trip_id': 70810, 'location_id': 18, 'time': '11:00:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:27:32.000000Z', 'updated_at': '2024-09-08T10:27:32.000000Z', 'station': {'id': 18, 'name_en': 'El Nasr Street', 'name_ar': 'شارع النصر', 'lat': '27.241535', 'long': '33.828012', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}]\n", "[{'id': 456090, 'trip_id': 70841, 'location_id': 23, 'time': '04:30:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:30:34.000000Z', 'updated_at': '2024-09-08T10:30:34.000000Z', 'station': {'id': 23, 'name_en': '6 October - El Hussary', 'name_ar': '6 اكتوبر - الحصري', 'lat': '29.968428', 'long': '30.938219', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 456091, 'trip_id': 70841, 'location_id': 46, 'time': '05:30:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:30:34.000000Z', 'updated_at': '2024-09-08T10:30:34.000000Z', 'station': {'id': 46, 'name_en': 'Ramsis', 'name_ar': 'رمسيس', 'lat': '30.063437', 'long': '31.252121', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 456092, 'trip_id': 70841, 'location_id': 50, 'time': '06:00:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:30:34.000000Z', 'updated_at': '2024-09-08T10:30:34.000000Z', 'station': {'id': 50, 'name_en': 'Sekka Club', 'name_ar': 'نادي السكة', 'lat': '30.057569550064', 'long': '31.************', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 456093, 'trip_id': 70841, 'location_id': 26, 'time': '06:30:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:30:34.000000Z', 'updated_at': '2024-09-08T10:30:34.000000Z', 'station': {'id': 26, 'name_en': 'Mehawar ElMoshier', 'name_ar': 'محور المشير', 'lat': '30.016827047205577', 'long': '31.39013424145697', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 456094, 'trip_id': 70841, 'location_id': 24, 'time': '11:00:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:30:34.000000Z', 'updated_at': '2024-09-08T10:30:34.000000Z', 'station': {'id': 24, 'name_en': 'Watanya-HRG', 'name_ar': 'محطة وطنية', 'lat': '27.215725', 'long': '33.817452', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}, {'id': 456095, 'trip_id': 70841, 'location_id': 18, 'time': '11:30:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:30:34.000000Z', 'updated_at': '2024-09-08T10:30:34.000000Z', 'station': {'id': 18, 'name_en': 'El Nasr Street', 'name_ar': 'شارع النصر', 'lat': '27.241535', 'long': '33.828012', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}]\n", "[{'id': 456300, 'trip_id': 70872, 'location_id': 23, 'time': '02:00:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:33:05.000000Z', 'updated_at': '2024-09-08T10:33:05.000000Z', 'station': {'id': 23, 'name_en': '6 October - El Hussary', 'name_ar': '6 اكتوبر - الحصري', 'lat': '29.968428', 'long': '30.938219', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 456301, 'trip_id': 70872, 'location_id': 71, 'time': '02:45:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:33:05.000000Z', 'updated_at': '2024-09-08T10:33:05.000000Z', 'station': {'id': 71, 'name_en': 'Giza', 'name_ar': 'الجيزه', 'lat': '30.018439755141', 'long': '31.************', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 456302, 'trip_id': 70872, 'location_id': 50, 'time': '03:30:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:33:05.000000Z', 'updated_at': '2024-09-08T10:33:05.000000Z', 'station': {'id': 50, 'name_en': 'Sekka Club', 'name_ar': 'نادي السكة', 'lat': '30.057569550064', 'long': '31.************', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 456303, 'trip_id': 70872, 'location_id': 26, 'time': '04:15:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:33:05.000000Z', 'updated_at': '2024-09-08T10:33:05.000000Z', 'station': {'id': 26, 'name_en': 'Mehawar ElMoshier', 'name_ar': 'محور المشير', 'lat': '30.016827047205577', 'long': '31.39013424145697', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 456304, 'trip_id': 70872, 'location_id': 24, 'time': '09:15:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:33:05.000000Z', 'updated_at': '2024-09-08T10:33:05.000000Z', 'station': {'id': 24, 'name_en': 'Watanya-HRG', 'name_ar': 'محطة وطنية', 'lat': '27.215725', 'long': '33.817452', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}, {'id': 456305, 'trip_id': 70872, 'location_id': 18, 'time': '09:30:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:33:05.000000Z', 'updated_at': '2024-09-08T10:33:05.000000Z', 'station': {'id': 18, 'name_en': 'El Nasr Street', 'name_ar': 'شارع النصر', 'lat': '27.241535', 'long': '33.828012', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}]\n", "[{'id': 456844, 'trip_id': 70934, 'location_id': 23, 'time': '15:00:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:47:19.000000Z', 'updated_at': '2024-09-08T10:47:19.000000Z', 'station': {'id': 23, 'name_en': '6 October - El Hussary', 'name_ar': '6 اكتوبر - الحصري', 'lat': '29.968428', 'long': '30.938219', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 456845, 'trip_id': 70934, 'location_id': 71, 'time': '16:00:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:47:19.000000Z', 'updated_at': '2024-09-08T10:47:19.000000Z', 'station': {'id': 71, 'name_en': 'Giza', 'name_ar': 'الجيزه', 'lat': '30.018439755141', 'long': '31.************', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 456846, 'trip_id': 70934, 'location_id': 50, 'time': '16:30:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:47:19.000000Z', 'updated_at': '2024-09-08T10:47:19.000000Z', 'station': {'id': 50, 'name_en': 'Sekka Club', 'name_ar': 'نادي السكة', 'lat': '30.057569550064', 'long': '31.************', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 456847, 'trip_id': 70934, 'location_id': 26, 'time': '17:00:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:47:19.000000Z', 'updated_at': '2024-09-08T10:47:19.000000Z', 'station': {'id': 26, 'name_en': 'Mehawar ElMoshier', 'name_ar': 'محور المشير', 'lat': '30.016827047205577', 'long': '31.39013424145697', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 456850, 'trip_id': 70934, 'location_id': 61, 'time': '22:30:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:47:19.000000Z', 'updated_at': '2024-09-08T10:47:19.000000Z', 'station': {'id': 61, 'name_en': 'Al Ahyaa', 'name_ar': 'الأحياء', 'lat': '27.297449871582913', 'long': '33.74555800129701', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}, {'id': 456851, 'trip_id': 70934, 'location_id': 24, 'time': '23:00:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:47:19.000000Z', 'updated_at': '2024-09-08T10:47:19.000000Z', 'station': {'id': 24, 'name_en': 'Watanya-HRG', 'name_ar': 'محطة وطنية', 'lat': '27.215725', 'long': '33.817452', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}, {'id': 456852, 'trip_id': 70934, 'location_id': 18, 'time': '23:15:00', 'date': '2024-10-11', 'created_at': '2024-09-08T10:47:19.000000Z', 'updated_at': '2024-09-08T10:47:19.000000Z', 'station': {'id': 18, 'name_en': 'El Nasr Street', 'name_ar': 'شارع النصر', 'lat': '27.241535', 'long': '33.828012', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}]\n", "[{'id': 466608, 'trip_id': 72474, 'location_id': 23, 'time': '00:01:00', 'date': '2024-10-11', 'created_at': '2024-09-22T07:42:35.000000Z', 'updated_at': '2024-09-22T07:42:35.000000Z', 'station': {'id': 23, 'name_en': '6 October - El Hussary', 'name_ar': '6 اكتوبر - الحصري', 'lat': '29.968428', 'long': '30.938219', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 466609, 'trip_id': 72474, 'location_id': 71, 'time': '00:45:00', 'date': '2024-10-11', 'created_at': '2024-09-22T07:42:35.000000Z', 'updated_at': '2024-09-22T07:42:35.000000Z', 'station': {'id': 71, 'name_en': 'Giza', 'name_ar': 'الجيزه', 'lat': '30.018439755141', 'long': '31.************', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 466610, 'trip_id': 72474, 'location_id': 50, 'time': '01:30:00', 'date': '2024-10-11', 'created_at': '2024-09-22T07:42:35.000000Z', 'updated_at': '2024-09-22T07:42:35.000000Z', 'station': {'id': 50, 'name_en': 'Sekka Club', 'name_ar': 'نادي السكة', 'lat': '30.057569550064', 'long': '31.************', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 466611, 'trip_id': 72474, 'location_id': 26, 'time': '02:00:00', 'date': '2024-10-11', 'created_at': '2024-09-22T07:42:35.000000Z', 'updated_at': '2024-09-22T07:42:35.000000Z', 'station': {'id': 26, 'name_en': 'Mehawar ElMoshier', 'name_ar': 'محور المشير', 'lat': '30.016827047205577', 'long': '31.39013424145697', 'city_id': 15, 'city': {'id': 15, 'uuid': 'ab995762-73dc-4394-9dc9-a7da97cd2c7f', 'code': 'GZ', 'name_en': 'Giza\\\\Cairo', 'name_ar': 'الجيزة/القاهرة', 'lat': '30.04167', 'long': '31.23528', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city151644292882.png', 'created_at': '2021-11-29T14:22:17.000000Z', 'updated_at': '2023-11-05T10:46:15.000000Z'}}}, {'id': 466614, 'trip_id': 72474, 'location_id': 61, 'time': '07:30:00', 'date': '2024-10-11', 'created_at': '2024-09-22T07:42:35.000000Z', 'updated_at': '2024-09-22T07:42:35.000000Z', 'station': {'id': 61, 'name_en': 'Al Ahyaa', 'name_ar': 'الأحياء', 'lat': '27.297449871582913', 'long': '33.74555800129701', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}, {'id': 466615, 'trip_id': 72474, 'location_id': 24, 'time': '08:00:00', 'date': '2024-10-11', 'created_at': '2024-09-22T07:42:35.000000Z', 'updated_at': '2024-09-22T07:42:35.000000Z', 'station': {'id': 24, 'name_en': 'Watanya-HRG', 'name_ar': 'محطة وطنية', 'lat': '27.215725', 'long': '33.817452', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}, {'id': 466616, 'trip_id': 72474, 'location_id': 18, 'time': '08:15:00', 'date': '2024-10-11', 'created_at': '2024-09-22T07:42:35.000000Z', 'updated_at': '2024-09-22T07:42:35.000000Z', 'station': {'id': 18, 'name_en': 'El Nasr Street', 'name_ar': 'شارع النصر', 'lat': '27.241535', 'long': '33.828012', 'city_id': 13, 'city': {'id': 13, 'uuid': '57c6e2e7-ebda-414e-8209-6a47b2d20acc', 'code': 'HRG', 'name_en': 'Hurghada', 'name_ar': 'الغردقة', 'lat': '27.25778', 'long': '33.81167', 'is_active': 1, 'is_seasonal': 0, 'seasonal_price': None, 'image': 'https://api.bluebus.com.eg/storage/images/images/cities/city131644292809.png', 'created_at': '2021-11-29T14:22:13.000000Z', 'updated_at': '2023-11-05T10:43:56.000000Z'}}}]\n"]}], "source": []}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Locations retrieval failed with status code 403\n", "Response: <!DOCTYPE html>\n", "<html lang=\"en\">\n", "<head>\n", "    <meta charset=\"UTF-8\" />\n", "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\" />\n", "    <title>Error <PERSON></title>\n", "    <script src=\"https://kit.fontawesome.com/66aa7c98b3.js\" crossorigin=\"anonymous\"></script>\n", "    <style>\n", "        @import url(\"https://fonts.googleapis.com/css2?family=Poppins:wght@400;700&display=swap\");\n", "        * { margin: 0; padding: 0; box-sizing: border-box; }\n", "        body { font-family: \"Poppins\", sans-serif; display: flex; justify-content: center; align-items: center; height: 100vh; }\n", "        .content { text-align: center; }\n", "        h1 { font-size: 2rem; font-weight: 700; }\n", "        p { font-size: 1rem; margin: 0.5rem 0; }\n", "        button { padding: 0.8rem; border-radius: 10px; border: none; background: #0046d4; color: #fff; font-size: 1rem; cursor: pointer; }\n", "    </style>\n", "</head>\n", "<body>\n", "    <div class=\"content\">\n", "        <img src=\"https://i.postimg.cc/2yrFyxKv/giphy.gif\" alt=\"gif\" style=\"width:100px; margin-bottom:1rem;\">\n", "        <h1>This page is gone.</h1>\n", "        <p>...maybe the page you're looking for is not found or never existed.</p>\n", "        <a href=\"https://www.google.com/search?q=bluebus.com.eg\" target=\"_blank\">\n", "            <button>Back to home <i class=\"far fa-hand-point-right\"></i></button>\n", "        </a>\n", "    </div>\n", "<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML=\"window.__CF$cv$params={r:'90988f28a85b5364',t:'MTczODE0Njc1NS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);\";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>\n", "</html>\n"]}], "source": ["# url = \"https://dev.bluebus.com.eg/api/agent/locations\"\n", "\n", "headers = {\n", "    'Authorization': f'Bearer {access_token}',  # Replace access_token with actual token\n", "    'Accept': 'application/json'\n", "}\n", "\n", "response = requests.get('https://api.bluebus.com.eg/api/agent/locations', headers=headers)\n", "\n", "# Check the response\n", "if response.status_code == 200:\n", "    print('Locations retrieval successful')\n", "    \n", "    # Pretty print the JSON response\n", "    response_json = response.json()\n", "    print(json.dumps(response_json, indent=4, ensure_ascii=False))\n", "\n", "else:\n", "    print(f'Locations retrieval failed with status code {response.status_code}')\n", "    print('Response:', response.text)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}