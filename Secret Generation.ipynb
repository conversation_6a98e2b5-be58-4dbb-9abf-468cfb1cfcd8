{"cells": [{"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["JzTwEzEJCE9GMWaqRpOMj1sCIIyksOY6bmy6lNC7FLw\n"]}], "source": ["# generate refresh token secret Key\n", "import secrets\n", "\n", "print(secrets.token_urlsafe(32))"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJhNzA1YWNmMi03OGM2LTQwYWUtYThjYS1jZThmNDQ3M2Q2ZGIiLCJyb2xlIjoidXNlciIsImlhdCI6MTcyNjczNDcxMiwiZXhwIjoxNzI2NzM0NzcyfQ.6xoss-dPTzW2p3cb5OCQuO0peoHKOq67epAsaDr0214\n", "{'user': {'user': {'firstname': '<PERSON>', 'lastname': '<PERSON><PERSON>', 'email': '<EMAIL>', 'isGoogleUser': True, 'phonenumber': '+20128437102', 'country': None, 'createdAt': '2024-09-08T08:09:37.000Z'}}, 'success': True, 'errorCode': None, 'message': None}\n"]}], "source": ["\n", "import requests\n", "import json\n", "\n", "url = \"http://3.68.159.193:3000/api/auth/google/callback\"\n", "data = {\"id_token\" : \"eyJhbGciOiJSUzI1NiIsImtpZCI6ImIyNjIwZDVlN2YxMzJiNTJhZmU4ODc1Y2RmMzc3NmMwNjQyNDlkMDQiLCJ0eXAiOiJKV1QifQ.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ezYab6iOgqQRmtg0LpEGndXMp7_v_xZpPiCX92v4r_BHlw-O7iEL_TWwK7LcTdIT24-8ys3AZ01mGHZLA1nfXo7m2qn0zWCa7msKZmdnIZeyFAJQ3_fkXdbRr74JkpNXHiQSAoyNvsdrysW9sJFYRqpMaB4xxxW-hf6NxyoJzBI3QUMCWzlFs4Z2NgUSdgFd7eyyr8_Wqy6MOoTScL4QN1cEcjBR8sHIwhZ1_TdofXlvPmnOj-caCJv0xi2nM-Cgmw5276sYbfFzWS-rihQrLPA4mRzgkioeybu2eGtePbWe2KfBmYNYULoRebYE_-4hb420SQl6Li9Mhl1T81cFJw, deviceToken: ebx2IKJGMkjXpkokwBvrm6:APA91bGPcQD0_-Pmcd2ZlW2RbtUiBP6utSbI_A_OKW3fnqLMewEGFl0YlB5Pwj0opjMCLS_leP3KhLzauHG0gZPRhBOpY8w7RJIRoHGr-tgpATKc4-Bjc9twc1hMzLMd8Jh3Q95OQYj9\",\n", "\"access_token\": \"******************************************************************************************************************************************************************************************************************************\"}\n", "\n", "response = requests.post(url, data=json.dumps(data), headers={\"Content-Type\": \"application/json\"})\n", "response = response.json()\n", "print(response['accessToken'])\n", "\n", "url = \"http://3.68.159.193:3000/api/profile\"\n", "headers = {\n", "    \"Authorization\" : \"Bearer \" + response['accessToken'],\n", "    \"Content-Type\" : \"application/json\"\n", "}\n", "\n", "response = requests.get(url, headers=headers)\n", "print(response.json())"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["b'\\xb8\\xfa\\x1ah\\xcd\\xbb1O\\xd9[W7\\xed\\xa8\\xb6\\x86\\xa9h\\xd1\\xf5LM\\xff[M\\x9c\\x81\\x01\\x9f{\\x12\\x1c'\n"]}], "source": ["import os\n", "def generate_secret_key():\n", "    secret_key = os.urandom(32)  # 32 bytes = 256-bit key\n", "    return secret_key\n", "\n", "print(generate_secret_key())"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}