{"cells": [{"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["c:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\Lib\\site-packages\\urllib3\\connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'obs.enr.gov.eg'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}], "source": ["import requests\n", "\n", "def get_data():\n", "    url = \"https://obs.enr.gov.eg/api/v1/tickets/search?from=606534187276566625&to=606535392467877956&transfers=false&with_reservations=true&without_reservations=false&skip_places_information=true&departureDate=2024-11-28&searchMode=WEB&project=enr\"\n", "    response = requests.get(url, verify=False)  # Disable SSL verification\n", "    data = response.json()\n", "    return data\n", "\n", "data = get_data()\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 175, 'totalOffsetFinish': 1010, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645600', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516481', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516502', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516507', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776019', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229822', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229846', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877938', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877946', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 0, 'standAvailableSeats': 173, 'fromDate': '2024-11-28T08:40:00.000+02:00', 'finishDate': '2024-11-28T22:35:00.000+02:00', 'duration': 835, 'train': {'id': '986478100490299149', 'name': '3008', 'params': {}, 'cost': 19500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '824578948489805818', 'name': '5', 'type': 'COACH', 'params': {'seats_count': '74', 'seatCountWithoutReservation': '173', 'no_seats': 'false', 'sortType': 'bottom', 'passageway': '2', 'coach_rows': '4', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210061', 'createdDate': '2022-03-02T17:27:21.756+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 3', 'name': 'AC 3', 'key': 'enr_coach_class', 'longV': '10055', 'params': {'pax_class': '55', 'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'sales_tax': '1', 'no_seats': '0', 'templateName': 'ENR Coach Class', 'en': 'AC 3', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'specific_priority': '', 'key': 'enr_coach_class'}, 'implClass': 'java.lang.String', 'seqno': 30, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, 'cost': 19500, 'workOrderId': '986478226249164557', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '19500', 'Specific': '19500', 'No seat': '19500', 'BASE': '19500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '687405386327654366', 'createdDate': '2022-08-10T23:04:15.992+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC3_TD', 'name': 'AC 3', 'key': 'enr_train_description', 'integerV': 25, 'params': {'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'templateName': 'Enr Train Description', 'stand_limitAC 3': '5', 'en': 'AC 3', 'day_limitAC 3': '360', 'used': 'service_point,service,fare', 'support_classes': 'GA 2,AC 3', 'Sheet': 'ImpField', 'stand_limitGA 2': '5', 'day_limitGA 2': '360', 'key': 'enr_train_description'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210061': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230004', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986478100490233613', 'serviceId': '986478100490299149', 'startingPrice': 19500, 'durationInMMHH': '13:55'}], 'classesCostMap': {'1210061': 19500}, 'profileMap': {'1210061': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390282645600', '606535390819516470', '606535390819516481', '606535390819516493', '606535390819516502', '606535390819516507', '606535391364775989', '606535391364775997', '606535391364776001', '606535391364776008', '606535391364776015', '606535391364776019', '606535391364776027', '606535391914229815', '606535391914229822', '606535391914229829', '606535391914229834', '606535391914229842', '606535391914229846', '606535391914229854', '606535392467877938', '606535392467877941', '606535392467877944', '606535392467877946', '606535392467877956'], 'coachWorkOrderId': '986478226249164557'}], 'specialId': '1230004', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 19500, 'distance': 879, 'duration': 835, 'timeFinish': '2024-11-28T22:35:00.000+02:00', 'seatsAvailable': 0, 'timeStart': '2024-11-28T08:40:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 250, 'totalOffsetFinish': 1030, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775993', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775995', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877946', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 23, 'standAvailableSeats': 19, 'fromDate': '2024-11-28T00:20:00.000+02:00', 'finishDate': '2024-11-28T13:20:00.000+02:00', 'duration': 780, 'train': {'id': '986329966287267597', 'name': '1902', 'params': {}, 'cost': 24500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '950753799231964133', 'name': '6', 'type': 'COACH', 'params': {'seats_count': '60', 'seatCountWithoutReservation': '19', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535876171792444', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986329966371546893', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535876171792444', 'name': '8', 'type': 'COACH', 'params': {'seats_count': '60', 'code': '6', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '6'}, 'availableSeats': ['606535876171792480', '606535876171792482', '606535876171792483', '606535876599611452', '606535876171792447', '606535876599611455'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986332454673786637', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535878239584314', 'name': '2', 'type': 'COACH', 'params': {'seats_count': '44', 'code': '15', 'no_seats': '', 'coach_cols': '4', 'coach_rows': '3', 'locked': 'true', 'seatCount': '7'}, 'availableSeats': ['606535878239584353', '606535878642237491', '606535878642237490', '606535878239584316', '606535878239584317', '606535878239584318', '606535878239584319'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986332454673524493', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535876171792444', 'name': '7', 'type': 'COACH', 'params': {'seats_count': '60', 'code': '6', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '10'}, 'availableSeats': ['606535876171792448', '606535876599611456', '606535876171792450', '606535876599611458', '606535876171792451', '606535876599611459', '606535876171792456', '606535876171792458', '606535876171792459', '606535876171792461'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986329966371481357', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555857991', 'createdDate': '2021-12-30T09:34:40.480+02:00', 'valueType': 'java.lang.String', 'shortName': 'Special', 'name': 'Special', 'key': 'enr_train_description', 'integerV': 10, 'params': {'code': 'Special', 'en': 'Special', 'used': 'service_point,service,fare', 'day_limitAC 2': '360', 'day_limitAC 1': '360', 'voucher_exclude_trains': '', 'medals_exclude_trains': '2', 'ar': 'خاص', 'templateName': 'Enr Train Description', 'stand_limitAC 1': '5', 'stand_limitAC 2': '5', 'sourceAC 2': \"'quota'\", 'sourceAC 1': \"'quota'\", 'support_classes': 'AC 1,AC 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': '2'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"خاص\",\"en\":\"Special\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'خاص', 'en': 'Special'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230005', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986329966287202061', 'serviceId': '986329966287267597', 'startingPrice': 24500, 'durationInMMHH': '13:00'}], 'classesCostMap': {'1210000': 38000, '1210001': 24500}, 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535391364775989', '606535391364775993', '606535391364775995', '606535391364775997', '606535391364776001', '606535391364776008', '606535391364776015', '606535391364776027', '606535391914229815', '606535391914229829', '606535391914229842', '606535391914229854', '606535392467877944', '606535392467877946', '606535392467877956'], 'coachWorkOrderId': '986332454673524493'}], 'specialId': '1230005', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 24500, 'distance': 879, 'duration': 780, 'timeFinish': '2024-11-28T13:20:00.000+02:00', 'seatsAvailable': 23, 'timeStart': '2024-11-28T00:20:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 210, 'totalOffsetFinish': 1180, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645600', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775993', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775995', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776004', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776011', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776013', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776019', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776023', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776025', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776033', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229820', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229822', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229845', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229846', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229849', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877938', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877940', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877946', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877948', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 0, 'standAvailableSeats': 3460, 'fromDate': '2024-11-28T15:30:00.000+02:00', 'finishDate': '2024-11-29T07:40:00.000+02:00', 'duration': 970, 'train': {'id': '986571672270612237', 'name': '164', 'params': {}, 'cost': 11500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '658405141430403053', 'name': '5', 'type': 'COACH', 'params': {'seats_count': '78', 'code': '41', 'seatCountWithoutReservation': '3460', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210059', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'GA 2', 'name': 'GA 2', 'key': 'enr_coach_class', 'longV': '10059', 'params': {'ar': 'ثالثة تهوية', 'en': 'GA 2', 'key': 'enr_coach_class', 'code': 'GA 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '59', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 50, 'localization': '{\"ar\":\"ثالثة تهوية\",\"en\":\"GA 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة تهوية', 'en': 'GA 2'}}, 'cost': 11500, 'workOrderId': '986571672277952269', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '11500', 'Specific': '11500', 'No seat': '11500', 'BASE': '11500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555858002', 'createdDate': '2021-12-30T09:34:40.506+02:00', 'valueType': 'java.lang.String', 'shortName': 'Third Good Air', 'name': 'Third Good Air', 'key': 'enr_train_description', 'integerV': 30, 'params': {'code': 'Third Good Air', 'en': 'Third Good Air', 'used': 'service_point,service,fare', 'voucher_exclude_trains': '', 'stand_limitGA 2': '100', 'day_limitGA 2': '360', 'medals_exclude_trains': '', 'ar': 'ثالثة تهوية', 'suburban_line': 'false', 'templateName': 'Enr Train Description', 'support_classes': 'GA 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': ''}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"ثالثة تهوية\",\"en\":\"Third Good Air\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة تهوية', 'en': 'Third Good Air'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210059': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230004', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986571672270546701', 'serviceId': '986571672270612237', 'startingPrice': 11500, 'durationInMMHH': '16:10'}], 'classesCostMap': {'1210059': 11500}, 'profileMap': {'1210059': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390282645600', '606535390819516470', '606535390819516493', '606535391364775989', '606535391364775993', '606535391364775995', '606535391364775997', '606535391364776001', '606535391364776004', '606535391364776008', '606535391364776011', '606535391364776013', '606535391364776015', '606535391364776019', '606535391364776023', '606535391364776025', '606535391364776027', '606535391364776033', '606535391914229815', '606535391914229820', '606535391914229822', '606535391914229829', '606535391914229834', '606535391914229842', '606535391914229845', '606535391914229846', '606535391914229849', '606535391914229854', '606535392467877938', '606535392467877940', '606535392467877941', '606535392467877944', '606535392467877946', '606535392467877948', '606535392467877956'], 'coachWorkOrderId': '986571672277952269'}], 'specialId': '1230004', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 11500, 'distance': 879, 'duration': 970, 'timeFinish': '2024-11-29T07:40:00.000+02:00', 'seatsAvailable': 0, 'timeStart': '2024-11-28T15:30:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 215, 'totalOffsetFinish': 1100, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645600', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516481', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516484', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516502', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516507', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776011', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776019', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776023', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776025', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776033', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229820', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229822', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229846', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877938', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877946', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 6, 'standAvailableSeats': 27, 'fromDate': '2024-11-28T21:25:00.000+02:00', 'finishDate': '2024-11-29T12:10:00.000+02:00', 'duration': 885, 'train': {'id': '986659571177891597', 'name': '88', 'params': {}, 'cost': 38000, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '950751353545491429', 'name': '3', 'type': 'COACH', 'params': {'seats_count': '44', 'seatCountWithoutReservation': '8', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535878239584314', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986659720275765005', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535876171792444', 'name': '7', 'type': 'COACH', 'params': {'seats_count': '60', 'code': '6', 'seatCountWithoutReservation': '19', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986659720276158221', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950753799231964133', 'name': '6', 'type': 'COACH', 'params': {'seats_count': '60', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535876171792444', 'seatCount': '4'}, 'availableSeats': ['950753799232619493', '950753799232553957', '950753799232488421', '950753799232422885'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986659720275699469', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950751353545491429', 'name': '1', 'type': 'COACH', 'params': {'seats_count': '44', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535878239584314', 'seatCount': '2'}, 'availableSeats': ['950751353546540005', '950751353546474469'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986659720275830541', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555857991', 'createdDate': '2021-12-30T09:34:40.480+02:00', 'valueType': 'java.lang.String', 'shortName': 'Special', 'name': 'Special', 'key': 'enr_train_description', 'integerV': 10, 'params': {'code': 'Special', 'en': 'Special', 'used': 'service_point,service,fare', 'day_limitAC 2': '360', 'day_limitAC 1': '360', 'voucher_exclude_trains': '', 'medals_exclude_trains': '2', 'ar': 'خاص', 'templateName': 'Enr Train Description', 'stand_limitAC 1': '5', 'stand_limitAC 2': '5', 'sourceAC 2': \"'quota'\", 'sourceAC 1': \"'quota'\", 'support_classes': 'AC 1,AC 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': '2'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"خاص\",\"en\":\"Special\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'خاص', 'en': 'Special'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230005', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986659571177826061', 'serviceId': '986659571177891597', 'startingPrice': 24500, 'durationInMMHH': '14:45'}], 'classesCostMap': {'1210000': 38000, '1210001': 24500}, 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390282645600', '606535390819516470', '606535390819516481', '606535390819516484', '606535390819516493', '606535390819516502', '606535390819516507', '606535391364775989', '606535391364775997', '606535391364776001', '606535391364776008', '606535391364776011', '606535391364776015', '606535391364776019', '606535391364776023', '606535391364776025', '606535391364776027', '606535391364776033', '606535391914229815', '606535391914229820', '606535391914229822', '606535391914229829', '606535391914229834', '606535391914229842', '606535391914229846', '606535391914229854', '606535392467877938', '606535392467877941', '606535392467877944', '606535392467877946', '606535392467877956'], 'coachWorkOrderId': '986659720275765005'}], 'specialId': '1230005', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 24500, 'distance': 879, 'duration': 885, 'timeFinish': '2024-11-29T12:10:00.000+02:00', 'seatsAvailable': 6, 'timeStart': '2024-11-28T21:25:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 180, 'totalOffsetFinish': 1000, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 18, 'standAvailableSeats': 17, 'fromDate': '2024-11-28T22:20:00.000+02:00', 'finishDate': '2024-11-29T12:00:00.000+02:00', 'duration': 820, 'train': {'id': '986682289080641293', 'name': '1088', 'params': {}, 'cost': 33000, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '924657693258416189', 'name': '5', 'type': 'COACH', 'params': {'seats_count': '62', 'seatCountWithoutReservation': '17', 'no_seats': 'false', 'sortType': 'free', 'passageway': '2', 'coach_rows': '4', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 33000, 'workOrderId': '986682289089685261', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '33000', 'Specific': '41500', 'No seat': '33000', 'BASE': '33000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '924657693258416189', 'name': '2', 'type': 'COACH', 'params': {'seats_count': '62', 'no_seats': 'false', 'sortType': 'free', 'passageway': '2', 'coach_rows': '4', 'locked': 'true', 'seatCount': '18'}, 'availableSeats': ['924657700174823474', '924657693258416209', '924657693258416211', '924657693258416213', '924657700174823479', '924657693258416214', '924657693258416220', '924657700174823487', '924657693258416190', '924657693258416223', '924657700174823485', '924657693258416226', '924657700174823488', '924657700174823489', '924657700174823497', '924657693258416204', '924657693258416205', '924657693258416207'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 33000, 'workOrderId': '986682289089488653', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '33000', 'Specific': '41500', 'No seat': '33000', 'BASE': '33000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555857991', 'createdDate': '2021-12-30T09:34:40.480+02:00', 'valueType': 'java.lang.String', 'shortName': 'Special', 'name': 'Special', 'key': 'enr_train_description', 'integerV': 10, 'params': {'code': 'Special', 'en': 'Special', 'used': 'service_point,service,fare', 'day_limitAC 2': '360', 'day_limitAC 1': '360', 'voucher_exclude_trains': '', 'medals_exclude_trains': '2', 'ar': 'خاص', 'templateName': 'Enr Train Description', 'stand_limitAC 1': '5', 'stand_limitAC 2': '5', 'sourceAC 2': \"'quota'\", 'sourceAC 1': \"'quota'\", 'support_classes': 'AC 1,AC 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': '2'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"خاص\",\"en\":\"Special\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'خاص', 'en': 'Special'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210001': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '960826024954045114', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986682289080575757', 'serviceId': '986682289080641293', 'startingPrice': 33000, 'durationInMMHH': '13:40'}], 'classesCostMap': {'1210001': 33000}, 'profileMap': {'1210001': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535391364775989', '606535391364776008', '606535391914229815', '606535391914229829', '606535391914229854', '606535392467877944', '606535392467877956'], 'coachWorkOrderId': '986682289089685261'}], 'specialId': '960826024954045114', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 33000, 'distance': 879, 'duration': 820, 'timeFinish': '2024-11-29T12:00:00.000+02:00', 'seatsAvailable': 18, 'timeStart': '2024-11-28T22:20:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 205, 'totalOffsetFinish': 1000, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516502', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516507', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775995', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 0, 'standAvailableSeats': 948, 'fromDate': '2024-11-28T23:15:00.000+02:00', 'finishDate': '2024-11-29T12:30:00.000+02:00', 'duration': 795, 'train': {'id': '986684879834588941', 'name': '1008', 'params': {}, 'cost': 11500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '606535884589760609', 'name': '2', 'type': 'COACH', 'params': {'seats_count': '88', 'code': '36', 'seatCountWithoutReservation': '948', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210059', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'GA 2', 'name': 'GA 2', 'key': 'enr_coach_class', 'longV': '10059', 'params': {'ar': 'ثالثة تهوية', 'en': 'GA 2', 'key': 'enr_coach_class', 'code': 'GA 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '59', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 50, 'localization': '{\"ar\":\"ثالثة تهوية\",\"en\":\"GA 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة تهوية', 'en': 'GA 2'}}, 'cost': 11500, 'workOrderId': '986684879836948237', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '11500', 'Specific': '11500', 'No seat': '11500', 'BASE': '11500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555858002', 'createdDate': '2021-12-30T09:34:40.506+02:00', 'valueType': 'java.lang.String', 'shortName': 'Third Good Air', 'name': 'Third Good Air', 'key': 'enr_train_description', 'integerV': 30, 'params': {'code': 'Third Good Air', 'en': 'Third Good Air', 'used': 'service_point,service,fare', 'voucher_exclude_trains': '', 'stand_limitGA 2': '100', 'day_limitGA 2': '360', 'medals_exclude_trains': '', 'ar': 'ثالثة تهوية', 'suburban_line': 'false', 'templateName': 'Enr Train Description', 'support_classes': 'GA 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': ''}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"ثالثة تهوية\",\"en\":\"Third Good Air\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة تهوية', 'en': 'Third Good Air'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210059': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230004', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986684879834523405', 'serviceId': '986684879834588941', 'startingPrice': 11500, 'durationInMMHH': '13:15'}], 'classesCostMap': {'1210059': 11500}, 'profileMap': {'1210059': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390819516470', '606535390819516493', '606535390819516502', '606535390819516507', '606535391364775989', '606535391364775995', '606535391364775997', '606535391364776001', '606535391364776008', '606535391364776015', '606535391364776027', '606535391914229815', '606535391914229829', '606535391914229834', '606535391914229842', '606535391914229854', '606535392467877941', '606535392467877944', '606535392467877956'], 'coachWorkOrderId': '986684879836948237'}], 'specialId': '1230004', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 11500, 'distance': 879, 'duration': 795, 'timeFinish': '2024-11-29T12:30:00.000+02:00', 'seatsAvailable': 0, 'timeStart': '2024-11-28T23:15:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 180, 'totalOffsetFinish': 945, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 22, 'standAvailableSeats': 8, 'fromDate': '2024-11-28T23:00:00.000+02:00', 'finishDate': '2024-11-29T11:45:00.000+02:00', 'duration': 765, 'train': {'id': '986692217466660621', 'name': '2008', 'params': {}, 'cost': 38000, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '606535878239584314', 'name': '4', 'type': 'COACH', 'params': {'seats_count': '44', 'code': '15', 'seatCountWithoutReservation': '8', 'no_seats': '', 'coach_cols': '4', 'coach_rows': '3', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986692311090079501', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535876171792444', 'name': '7', 'type': 'COACH', 'params': {'seats_count': '60', 'code': '6', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '4'}, 'availableSeats': ['606535876171792480', '606535876171792482', '606535876171792483', '606535876171792447'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986692311090145037', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950753799231964133', 'name': '6', 'type': 'COACH', 'params': {'seats_count': '60', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535876171792444', 'seatCount': '11'}, 'availableSeats': ['950753799240090597', '950753799240025061', '950753799239959525', '950753799239893989', '950753799239828453', '950753799239762917', '950753799239697381', '950753799240352741', '950753799240287205', '950753799240221669', '950753799240156133'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986692311089751821', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950751353545491429', 'name': '1', 'type': 'COACH', 'params': {'seats_count': '44', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535878239584314', 'seatCount': '3'}, 'availableSeats': ['950751353548375013', '950751353548309477', '950751353548440549'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986692311089882893', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535878239584314', 'name': '2', 'type': 'COACH', 'params': {'seats_count': '44', 'code': '15', 'no_seats': '', 'coach_cols': '4', 'coach_rows': '3', 'locked': 'true', 'seatCount': '4'}, 'availableSeats': ['606535878239584345', '606535878239584348', '606535878239584350', '606535878239584351'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986692311090013965', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555857991', 'createdDate': '2021-12-30T09:34:40.480+02:00', 'valueType': 'java.lang.String', 'shortName': 'Special', 'name': 'Special', 'key': 'enr_train_description', 'integerV': 10, 'params': {'code': 'Special', 'en': 'Special', 'used': 'service_point,service,fare', 'day_limitAC 2': '360', 'day_limitAC 1': '360', 'voucher_exclude_trains': '', 'medals_exclude_trains': '2', 'ar': 'خاص', 'templateName': 'Enr Train Description', 'stand_limitAC 1': '5', 'stand_limitAC 2': '5', 'sourceAC 2': \"'quota'\", 'sourceAC 1': \"'quota'\", 'support_classes': 'AC 1,AC 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': '2'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"خاص\",\"en\":\"Special\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'خاص', 'en': 'Special'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230005', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986692217466595085', 'serviceId': '986692217466660621', 'startingPrice': 24500, 'durationInMMHH': '12:45'}], 'classesCostMap': {'1210000': 38000, '1210001': 24500}, 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535391364775989', '606535391364776008', '606535391364776015', '606535391364776027', '606535391914229815', '606535391914229829', '606535391914229854', '606535392467877941', '606535392467877944', '606535392467877956'], 'coachWorkOrderId': '986692311090079501'}], 'specialId': '1230005', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 24500, 'distance': 879, 'duration': 765, 'timeFinish': '2024-11-29T11:45:00.000+02:00', 'seatsAvailable': 22, 'timeStart': '2024-11-28T23:00:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 185, 'totalOffsetFinish': 945, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516481', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516502', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 0, 'standAvailableSeats': 173, 'fromDate': '2024-11-28T00:50:00.000+02:00', 'finishDate': '2024-11-28T13:30:00.000+02:00', 'duration': 760, 'train': {'id': '986354900182443789', 'name': '3006', 'params': {}, 'cost': 19500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '824578948489805818', 'name': '5', 'type': 'COACH', 'params': {'seats_count': '74', 'seatCountWithoutReservation': '173', 'no_seats': 'false', 'sortType': 'bottom', 'passageway': '2', 'coach_rows': '4', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210061', 'createdDate': '2022-03-02T17:27:21.756+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 3', 'name': 'AC 3', 'key': 'enr_coach_class', 'longV': '10055', 'params': {'pax_class': '55', 'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'sales_tax': '1', 'no_seats': '0', 'templateName': 'ENR Coach Class', 'en': 'AC 3', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'specific_priority': '', 'key': 'enr_coach_class'}, 'implClass': 'java.lang.String', 'seqno': 30, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, 'cost': 19500, 'workOrderId': '986354900201252621', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '19500', 'Specific': '19500', 'No seat': '19500', 'BASE': '19500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '687405386327654366', 'createdDate': '2022-08-10T23:04:15.992+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC3_TD', 'name': 'AC 3', 'key': 'enr_train_description', 'integerV': 25, 'params': {'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'templateName': 'Enr Train Description', 'stand_limitAC 3': '5', 'en': 'AC 3', 'day_limitAC 3': '360', 'used': 'service_point,service,fare', 'support_classes': 'GA 2,AC 3', 'Sheet': 'ImpField', 'stand_limitGA 2': '5', 'day_limitGA 2': '360', 'key': 'enr_train_description'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210061': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230004', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986354900182378253', 'serviceId': '986354900182443789', 'startingPrice': 19500, 'durationInMMHH': '12:40'}], 'classesCostMap': {'1210061': 19500}, 'profileMap': {'1210061': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390819516470', '606535390819516481', '606535390819516493', '606535390819516502', '606535391364775989', '606535391364776008', '606535391364776027', '606535391914229815', '606535391914229829', '606535391914229854', '606535392467877944', '606535392467877956'], 'coachWorkOrderId': '986354900201252621'}], 'specialId': '1230004', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 19500, 'distance': 879, 'duration': 760, 'timeFinish': '2024-11-28T13:30:00.000+02:00', 'seatsAvailable': 0, 'timeStart': '2024-11-28T00:50:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 855, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516502', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516507', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516513', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775993', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776004', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776019', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776025', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776033', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229822', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229846', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 0, 'standAvailableSeats': 152, 'fromDate': '2024-11-28T00:05:00.000+02:00', 'finishDate': '2024-11-28T14:20:00.000+02:00', 'duration': 855, 'train': {'id': '986392918172051213', 'name': '1010', 'params': {}, 'cost': 19500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '656112980286504920', 'name': '3', 'type': 'COACH', 'params': {'seats_count': '88', 'code': '36', 'seatCountWithoutReservation': '152', 'no_seats': '', 'coach_cols': '5', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210061', 'createdDate': '2022-03-02T17:27:21.756+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 3', 'name': 'AC 3', 'key': 'enr_coach_class', 'longV': '10055', 'params': {'pax_class': '55', 'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'sales_tax': '1', 'no_seats': '0', 'templateName': 'ENR Coach Class', 'en': 'AC 3', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'specific_priority': '', 'key': 'enr_coach_class'}, 'implClass': 'java.lang.String', 'seqno': 30, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, 'cost': 19500, 'workOrderId': '986392918225331981', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '19500', 'Specific': '19500', 'No seat': '19500', 'BASE': '19500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '687405386327654366', 'createdDate': '2022-08-10T23:04:15.992+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC3_TD', 'name': 'AC 3', 'key': 'enr_train_description', 'integerV': 25, 'params': {'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'templateName': 'Enr Train Description', 'stand_limitAC 3': '5', 'en': 'AC 3', 'day_limitAC 3': '360', 'used': 'service_point,service,fare', 'support_classes': 'GA 2,AC 3', 'Sheet': 'ImpField', 'stand_limitGA 2': '5', 'day_limitGA 2': '360', 'key': 'enr_train_description'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210061': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230004', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986392918171985677', 'serviceId': '986392918172051213', 'startingPrice': 19500, 'durationInMMHH': '14:15'}], 'classesCostMap': {'1210061': 19500}, 'profileMap': {'1210061': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390819516470', '606535390819516493', '606535390819516502', '606535390819516507', '606535390819516513', '606535391364775989', '606535391364775993', '606535391364775997', '606535391364776001', '606535391364776004', '606535391364776008', '606535391364776015', '606535391364776019', '606535391364776025', '606535391364776027', '606535391364776033', '606535391914229815', '606535391914229822', '606535391914229829', '606535391914229834', '606535391914229842', '606535391914229846', '606535391914229854', '606535392467877944', '606535392467877956'], 'coachWorkOrderId': '986392918225331981'}], 'specialId': '1230004', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 19500, 'distance': 879, 'duration': 855, 'timeFinish': '2024-11-28T14:20:00.000+02:00', 'seatsAvailable': 0, 'timeStart': '2024-11-28T00:05:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 865, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775993', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776004', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776011', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776019', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776023', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776025', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776033', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229820', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229822', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229846', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877938', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877946', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 62, 'standAvailableSeats': 0, 'fromDate': '2024-11-28T08:00:00.000+02:00', 'finishDate': '2024-11-28T22:25:00.000+02:00', 'duration': 865, 'train': {'id': '986511325089179405', 'name': '980', 'params': {}, 'cost': 24500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '950753799288521701', 'name': '3', 'type': 'COACH', 'params': {'seats_count': '47', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535875345514549', 'seatCount': '18'}, 'availableSeats': ['950753799295665125', '950753799295599589', '950753799295534053', '950753799295468517', '950753799295402981', '950753799295337445', '950753799295271909', '950753799295206373', '950753799295140837', '950753799295075301', '950753799295009765', '950753799294026725', '950753799293961189', '950753799293895653', '950753799293830117', '950753799293764581', '950753799293699045', '950753799295730661'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986511408595740429', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535876599611464', 'name': '10', 'type': 'COACH', 'params': {'seats_count': '64', 'code': '7', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '26'}, 'availableSeats': ['606535876599611472', '606535877019041872', '606535876599611475', '606535877019041875', '606535876599611481', '606535876599611484', '606535876599611485', '606535876599611486', '606535876599611487', '606535876599611488', '606535876599611490', '606535876599611491', '606535877019041842', '606535877019041843', '606535877019041844', '606535877019041845', '606535877019041846', '606535877019041847', '606535877019041848', '606535877019041849', '606535877019041850', '606535877019041851', '606535877019041852', '606535877019041853', '606535877019041854', '606535877019041855'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986511408608847629', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950753034885661669', 'name': '9', 'type': 'COACH', 'params': {'seats_count': '64', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535876599611464', 'seatCount': '18'}, 'availableSeats': ['950753034893067237', '950753034893198309', '950753034893132773', '950753034894115813', '950753034894050277', '950753034894181349', '950753034893853669', '950753034893788133', '950753034893984741', '950753034893919205', '950753034893591525', '950753034893525989', '950753034893722597', '950753034893657061', '950753034893329381', '950753034893263845', '950753034893460453', '950753034893394917'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986511408595609357', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555857991', 'createdDate': '2021-12-30T09:34:40.480+02:00', 'valueType': 'java.lang.String', 'shortName': 'Special', 'name': 'Special', 'key': 'enr_train_description', 'integerV': 10, 'params': {'code': 'Special', 'en': 'Special', 'used': 'service_point,service,fare', 'day_limitAC 2': '360', 'day_limitAC 1': '360', 'voucher_exclude_trains': '', 'medals_exclude_trains': '2', 'ar': 'خاص', 'templateName': 'Enr Train Description', 'stand_limitAC 1': '5', 'stand_limitAC 2': '5', 'sourceAC 2': \"'quota'\", 'sourceAC 1': \"'quota'\", 'support_classes': 'AC 1,AC 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': '2'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"خاص\",\"en\":\"Special\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'خاص', 'en': 'Special'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230005', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986511325089113869', 'serviceId': '986511325089179405', 'startingPrice': 24500, 'durationInMMHH': '14:25'}], 'classesCostMap': {'1210000': 38000, '1210001': 24500}, 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390819516470', '606535390819516493', '606535391364775989', '606535391364775993', '606535391364775997', '606535391364776001', '606535391364776004', '606535391364776008', '606535391364776011', '606535391364776015', '606535391364776019', '606535391364776023', '606535391364776025', '606535391364776027', '606535391364776033', '606535391914229815', '606535391914229820', '606535391914229822', '606535391914229829', '606535391914229834', '606535391914229842', '606535391914229846', '606535391914229854', '606535392467877938', '606535392467877941', '606535392467877944', '606535392467877946', '606535392467877956'], 'coachWorkOrderId': '986511408595740429'}], 'specialId': '1230005', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 24500, 'distance': 879, 'duration': 865, 'timeFinish': '2024-11-28T22:25:00.000+02:00', 'seatsAvailable': 62, 'timeStart': '2024-11-28T08:00:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 1025, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645581', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645582', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645592', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645600', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516468', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516474', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516477', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516481', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516482', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516484', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516486', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516488', 'params': {}, 'localizationMap': {}}, {'id': '606535393554202686', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516498', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516501', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516502', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516505', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516507', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516510', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516513', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775987', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775993', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775995', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776004', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776011', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776013', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776017', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776019', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776021', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776023', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776025', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776033', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229820', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229822', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229845', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229846', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229849', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229856', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229858', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877938', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877946', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877948', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 0, 'standAvailableSeats': 3460, 'fromDate': '2024-11-28T09:00:00.000+02:00', 'finishDate': '2024-11-29T02:05:00.000+02:00', 'duration': 1025, 'train': {'id': '986525762679744269', 'name': '80', 'params': {}, 'cost': 11500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '606535884589760609', 'name': '7', 'type': 'COACH', 'params': {'seats_count': '88', 'code': '36', 'seatCountWithoutReservation': '3460', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210059', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'GA 2', 'name': 'GA 2', 'key': 'enr_coach_class', 'longV': '10059', 'params': {'ar': 'ثالثة تهوية', 'en': 'GA 2', 'key': 'enr_coach_class', 'code': 'GA 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '59', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 50, 'localization': '{\"ar\":\"ثالثة تهوية\",\"en\":\"GA 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة تهوية', 'en': 'GA 2'}}, 'cost': 11500, 'workOrderId': '986526228881549069', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '11500', 'Specific': '11500', 'No seat': '11500', 'BASE': '11500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555858002', 'createdDate': '2021-12-30T09:34:40.506+02:00', 'valueType': 'java.lang.String', 'shortName': 'Third Good Air', 'name': 'Third Good Air', 'key': 'enr_train_description', 'integerV': 30, 'params': {'code': 'Third Good Air', 'en': 'Third Good Air', 'used': 'service_point,service,fare', 'voucher_exclude_trains': '', 'stand_limitGA 2': '100', 'day_limitGA 2': '360', 'medals_exclude_trains': '', 'ar': 'ثالثة تهوية', 'suburban_line': 'false', 'templateName': 'Enr Train Description', 'support_classes': 'GA 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': ''}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"ثالثة تهوية\",\"en\":\"Third Good Air\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة تهوية', 'en': 'Third Good Air'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210059': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230004', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986525762679285517', 'serviceId': '986525762679744269', 'startingPrice': 11500, 'durationInMMHH': '17:05'}], 'classesCostMap': {'1210059': 11500}, 'profileMap': {'1210059': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390282645581', '606535390282645582', '606535390282645592', '606535390282645600', '606535390819516468', '606535390819516470', '606535390819516474', '606535390819516477', '606535390819516481', '606535390819516482', '606535390819516484', '606535390819516486', '606535390819516488', '606535393554202686', '606535390819516493', '606535390819516498', '606535390819516501', '606535390819516502', '606535390819516505', '606535390819516507', '606535390819516510', '606535390819516513', '606535391364775987', '606535391364775989', '606535391364775993', '606535391364775995', '606535391364775997', '606535391364776001', '606535391364776004', '606535391364776008', '606535391364776011', '606535391364776013', '606535391364776015', '606535391364776017', '606535391364776019', '606535391364776021', '606535391364776023', '606535391364776025', '606535391364776027', '606535391364776033', '606535391914229815', '606535391914229820', '606535391914229822', '606535391914229829', '606535391914229834', '606535391914229842', '606535391914229845', '606535391914229846', '606535391914229849', '606535391914229854', '606535391914229856', '606535391914229858', '606535392467877938', '606535392467877941', '606535392467877944', '606535392467877946', '606535392467877948', '606535392467877956'], 'coachWorkOrderId': '986526228881549069'}], 'specialId': '1230004', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 11500, 'distance': 879, 'duration': 1025, 'timeFinish': '2024-11-29T02:05:00.000+02:00', 'seatsAvailable': 0, 'timeStart': '2024-11-28T09:00:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 820, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516481', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516502', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776019', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229820', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 0, 'standAvailableSeats': 158, 'fromDate': '2024-11-28T09:30:00.000+02:00', 'finishDate': '2024-11-28T23:10:00.000+02:00', 'duration': 820, 'train': {'id': '986533643964458765', 'name': '1004', 'params': {}, 'cost': 19500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '656112980286504920', 'name': '6', 'type': 'COACH', 'params': {'seats_count': '88', 'code': '36', 'seatCountWithoutReservation': '158', 'no_seats': '', 'coach_cols': '5', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210061', 'createdDate': '2022-03-02T17:27:21.756+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 3', 'name': 'AC 3', 'key': 'enr_coach_class', 'longV': '10055', 'params': {'pax_class': '55', 'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'sales_tax': '1', 'no_seats': '0', 'templateName': 'ENR Coach Class', 'en': 'AC 3', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'specific_priority': '', 'key': 'enr_coach_class'}, 'implClass': 'java.lang.String', 'seqno': 30, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, 'cost': 19500, 'workOrderId': '986533643998209805', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '19500', 'Specific': '19500', 'No seat': '19500', 'BASE': '19500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '687405386327654366', 'createdDate': '2022-08-10T23:04:15.992+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC3_TD', 'name': 'AC 3', 'key': 'enr_train_description', 'integerV': 25, 'params': {'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'templateName': 'Enr Train Description', 'stand_limitAC 3': '5', 'en': 'AC 3', 'day_limitAC 3': '360', 'used': 'service_point,service,fare', 'support_classes': 'GA 2,AC 3', 'Sheet': 'ImpField', 'stand_limitGA 2': '5', 'day_limitGA 2': '360', 'key': 'enr_train_description'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210061': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230004', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986533643964393229', 'serviceId': '986533643964458765', 'startingPrice': 19500, 'durationInMMHH': '13:40'}], 'classesCostMap': {'1210061': 19500}, 'profileMap': {'1210061': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390819516470', '606535390819516481', '606535390819516493', '606535390819516502', '606535391364775989', '606535391364775997', '606535391364776001', '606535391364776008', '606535391364776015', '606535391364776019', '606535391364776027', '606535391914229815', '606535391914229820', '606535391914229829', '606535391914229834', '606535391914229842', '606535391914229854', '606535392467877941', '606535392467877944', '606535392467877956'], 'coachWorkOrderId': '986533643998209805'}], 'specialId': '1230004', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 19500, 'distance': 879, 'duration': 820, 'timeFinish': '2024-11-28T23:10:00.000+02:00', 'seatsAvailable': 0, 'timeStart': '2024-11-28T09:30:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 775, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877946', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 54, 'standAvailableSeats': 24, 'fromDate': '2024-11-28T10:00:00.000+02:00', 'finishDate': '2024-11-28T22:55:00.000+02:00', 'duration': 775, 'train': {'id': '986538117027800845', 'name': '2010', 'params': {}, 'cost': 24500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '950753799288521701', 'name': '1', 'type': 'COACH', 'params': {'seats_count': '47', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535875345514549', 'seatCount': '2'}, 'availableSeats': ['950753799294026725', '950753799293961189'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986538117053622029', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535874502459461', 'name': '5', 'type': 'COACH', 'params': {'seats_count': '36', 'code': '2', 'seatCountWithoutReservation': '24', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986538117054146317', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535875345514549', 'name': '4', 'type': 'COACH', 'params': {'seats_count': '47', 'code': '4', 'no_seats': '', 'coach_cols': '4', 'coach_rows': '3', 'locked': 'true', 'seatCount': '12'}, 'availableSeats': ['606535875345514576', '606535875345514561', '606535875345514578', '606535875345514579', '606535875345514564', '606535875345514565', '606535875345514566', '606535875345514567', '606535875345514584', '606535875345514587', '606535875345514589', '606535875345514590'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986538117053687565', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950753799288521701', 'name': '3', 'type': 'COACH', 'params': {'seats_count': '47', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535875345514549', 'seatCount': '1'}, 'availableSeats': ['950753799294026725'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986538117053556493', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535876599611464', 'name': '10', 'type': 'COACH', 'params': {'seats_count': '64', 'code': '7', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '39'}, 'availableSeats': ['606535877019041856', '606535877019041857', '606535877019041858', '606535877019041859', '606535877019041860', '606535877019041861', '606535877019041862', '606535877019041863', '606535877019041865', '606535876599611467', '606535876599611468', '606535877019041868', '606535876599611469', '606535877019041869', '606535876599611470', '606535877019041870', '606535876599611471', '606535877019041871', '606535876599611472', '606535877019041872', '606535876599611473', '606535877019041873', '606535876599611474', '606535877019041874', '606535876599611475', '606535877019041875', '606535876599611476', '606535877019041876', '606535876599611477', '606535877019041877', '606535876599611478', '606535877019041878', '606535876599611479', '606535877019041879', '606535876599611480', '606535876599611482', '606535876599611483', '606535876599611485', '606535876599611489'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986538117053884173', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555857991', 'createdDate': '2021-12-30T09:34:40.480+02:00', 'valueType': 'java.lang.String', 'shortName': 'Special', 'name': 'Special', 'key': 'enr_train_description', 'integerV': 10, 'params': {'code': 'Special', 'en': 'Special', 'used': 'service_point,service,fare', 'day_limitAC 2': '360', 'day_limitAC 1': '360', 'voucher_exclude_trains': '', 'medals_exclude_trains': '2', 'ar': 'خاص', 'templateName': 'Enr Train Description', 'stand_limitAC 1': '5', 'stand_limitAC 2': '5', 'sourceAC 2': \"'quota'\", 'sourceAC 1': \"'quota'\", 'support_classes': 'AC 1,AC 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': '2'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"خاص\",\"en\":\"Special\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'خاص', 'en': 'Special'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230005', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986538117027735309', 'serviceId': '986538117027800845', 'startingPrice': 24500, 'durationInMMHH': '12:55'}], 'classesCostMap': {'1210000': 38000, '1210001': 24500}, 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390819516493', '606535391364775989', '606535391364776008', '606535391364776027', '606535391914229815', '606535391914229829', '606535391914229842', '606535391914229854', '606535392467877941', '606535392467877944', '606535392467877946', '606535392467877956'], 'coachWorkOrderId': '986538117053622029'}], 'specialId': '1230005', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 24500, 'distance': 879, 'duration': 775, 'timeFinish': '2024-11-28T22:55:00.000+02:00', 'seatsAvailable': 54, 'timeStart': '2024-11-28T10:00:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 865, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645600', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516502', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775993', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775995', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776004', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776011', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776019', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776023', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776025', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776033', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229820', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229822', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877946', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 42, 'standAvailableSeats': 30, 'fromDate': '2024-11-28T12:00:00.000+02:00', 'finishDate': '2024-11-29T02:25:00.000+02:00', 'duration': 865, 'train': {'id': '986571520866985741', 'name': '982', 'params': {}, 'cost': 24500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '950753799288521701', 'name': '3', 'type': 'COACH', 'params': {'seats_count': '47', 'seatCountWithoutReservation': '9', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535875345514549', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986571610002106125', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950753034885661669', 'name': '6', 'type': 'COACH', 'params': {'seats_count': '64', 'seatCountWithoutReservation': '21', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535876599611464', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986571610002040589', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950753034885661669', 'name': '9', 'type': 'COACH', 'params': {'seats_count': '64', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535876599611464', 'seatCount': '29'}, 'availableSeats': ['950753034893067237', '950753034893001701', '950753034893198309', '950753034893132773', '950753034892805093', '950753034892739557', '950753034892936165', '950753034892870629', '950753034892542949', '950753034892477413', '950753034892674021', '950753034892608485', '950753034892280805', '950753034892411877', '950753034894115813', '950753034894050277', '950753034894181349', '950753034893853669', '950753034893788133', '950753034893984741', '950753034893919205', '950753034893591525', '950753034893525989', '950753034893722597', '950753034893657061', '950753034893329381', '950753034893263845', '950753034893460453', '950753034893394917'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986571610001975053', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535875345514549', 'name': '4', 'type': 'COACH', 'params': {'seats_count': '47', 'code': '4', 'no_seats': '', 'coach_cols': '4', 'coach_rows': '3', 'locked': 'true', 'seatCount': '7'}, 'availableSeats': ['606535875345514576', '606535875345514579', '606535875345514585', '606535875345514588', '606535875345514589', '606535875345514590', '606535875345514591'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986571610002433805', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535876599611464', 'name': '10', 'type': 'COACH', 'params': {'seats_count': '64', 'code': '7', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '6'}, 'availableSeats': ['606535876599611488', '606535876599611490', '606535876599611491', '606535877019041849', '606535877019041852', '606535877019041855'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986571610002237197', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555857991', 'createdDate': '2021-12-30T09:34:40.480+02:00', 'valueType': 'java.lang.String', 'shortName': 'Special', 'name': 'Special', 'key': 'enr_train_description', 'integerV': 10, 'params': {'code': 'Special', 'en': 'Special', 'used': 'service_point,service,fare', 'day_limitAC 2': '360', 'day_limitAC 1': '360', 'voucher_exclude_trains': '', 'medals_exclude_trains': '2', 'ar': 'خاص', 'templateName': 'Enr Train Description', 'stand_limitAC 1': '5', 'stand_limitAC 2': '5', 'sourceAC 2': \"'quota'\", 'sourceAC 1': \"'quota'\", 'support_classes': 'AC 1,AC 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': '2'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"خاص\",\"en\":\"Special\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'خاص', 'en': 'Special'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230005', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986571520866920205', 'serviceId': '986571520866985741', 'startingPrice': 24500, 'durationInMMHH': '14:25'}], 'classesCostMap': {'1210000': 38000, '1210001': 24500}, 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390282645600', '606535390819516470', '606535390819516493', '606535390819516502', '606535391364775989', '606535391364775993', '606535391364775995', '606535391364775997', '606535391364776001', '606535391364776004', '606535391364776008', '606535391364776011', '606535391364776015', '606535391364776019', '606535391364776023', '606535391364776025', '606535391364776027', '606535391364776033', '606535391914229815', '606535391914229820', '606535391914229822', '606535391914229829', '606535391914229834', '606535391914229842', '606535391914229854', '606535392467877941', '606535392467877944', '606535392467877946', '606535392467877956'], 'coachWorkOrderId': '986571610002106125'}], 'specialId': '1230005', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 24500, 'distance': 879, 'duration': 865, 'timeFinish': '2024-11-29T02:25:00.000+02:00', 'seatsAvailable': 42, 'timeStart': '2024-11-28T12:00:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 765, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 71, 'standAvailableSeats': 33, 'fromDate': '2024-11-28T17:15:00.000+02:00', 'finishDate': '2024-11-29T06:00:00.000+02:00', 'duration': 765, 'train': {'id': '986651956103948045', 'name': '2006', 'params': {}, 'cost': 38000, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '606535876599611464', 'name': '12', 'type': 'COACH', 'params': {'seats_count': '64', 'code': '7', 'seatCountWithoutReservation': '24', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986651956120921869', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950753799288521701', 'name': '1', 'type': 'COACH', 'params': {'seats_count': '47', 'seatCountWithoutReservation': '9', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535875345514549', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986651956120659725', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535875345514549', 'name': '2', 'type': 'COACH', 'params': {'seats_count': '47', 'code': '4', 'no_seats': '', 'coach_cols': '4', 'coach_rows': '3', 'locked': 'true', 'seatCount': '6'}, 'availableSeats': ['606535875345514562', '606535875345514551', '606535875345514568', '606535875345514570', '606535875345514571', '606535875345514573'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986651956121052941', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950753034885661669', 'name': '9', 'type': 'COACH', 'params': {'seats_count': '64', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535876599611464', 'seatCount': '40'}, 'availableSeats': ['950753034890970085', '950753034890904549', '950753034891035621', '950753034890707941', '950753034890642405', '950753034890839013', '950753034890773477', '950753034890445797', '950753034890380261', '950753034890576869', '950753034890511333', '950753034890183653', '950753034890118117', '950753034890314725', '950753034890249189', '950753034891232229', '950753034891166693', '950753034891297765', '950753034893067237', '950753034893001701', '950753034893198309', '950753034893132773', '950753034892805093', '950753034892739557', '950753034892936165', '950753034892870629', '950753034892542949', '950753034892477413', '950753034892674021', '950753034892608485', '950753034892280805', '950753034892411877', '950753034892346341', '950753034885858277', '950753034885792741', '950753034893525989', '950753034893329381', '950753034893263845', '950753034893460453', '950753034893394917'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986651956120463117', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535876599611464', 'name': '8', 'type': 'COACH', 'params': {'seats_count': '64', 'code': '7', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '13'}, 'availableSeats': ['606535877019041856', '606535877019041858', '606535877019041859', '606535877019041864', '606535876599611466', '606535877019041866', '606535876599611467', '606535877019041867', '606535876599611469', '606535877019041869', '606535876599611470', '606535877019041870', '606535877019041871'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986651956120790797', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950753799288521701', 'name': '3', 'type': 'COACH', 'params': {'seats_count': '47', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535875345514549', 'seatCount': '12'}, 'availableSeats': ['950753799289308133', '950753799289242597', '950753799289177061', '950753799289111525', '950753799289045989', '950753799288980453', '950753799288914917', '950753799288849381', '950753799288783845', '950753799288718309', '950753799288652773', '950753799288587237'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986651956120594189', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555857991', 'createdDate': '2021-12-30T09:34:40.480+02:00', 'valueType': 'java.lang.String', 'shortName': 'Special', 'name': 'Special', 'key': 'enr_train_description', 'integerV': 10, 'params': {'code': 'Special', 'en': 'Special', 'used': 'service_point,service,fare', 'day_limitAC 2': '360', 'day_limitAC 1': '360', 'voucher_exclude_trains': '', 'medals_exclude_trains': '2', 'ar': 'خاص', 'templateName': 'Enr Train Description', 'stand_limitAC 1': '5', 'stand_limitAC 2': '5', 'sourceAC 2': \"'quota'\", 'sourceAC 1': \"'quota'\", 'support_classes': 'AC 1,AC 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': '2'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"خاص\",\"en\":\"Special\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'خاص', 'en': 'Special'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230005', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986651956103882509', 'serviceId': '986651956103948045', 'startingPrice': 24500, 'durationInMMHH': '12:45'}], 'classesCostMap': {'1210000': 38000, '1210001': 24500}, 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390819516493', '606535391364775989', '606535391364776008', '606535391364776027', '606535391914229815', '606535391914229829', '606535391914229854', '606535392467877944', '606535392467877956'], 'coachWorkOrderId': '986651956120659725'}], 'specialId': '1230005', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 24500, 'distance': 879, 'duration': 765, 'timeFinish': '2024-11-29T06:00:00.000+02:00', 'seatsAvailable': 71, 'timeStart': '2024-11-28T17:15:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 770, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 0, 'standAvailableSeats': 155, 'fromDate': '2024-11-28T17:30:00.000+02:00', 'finishDate': '2024-11-29T06:20:00.000+02:00', 'duration': 770, 'train': {'id': '986654276338460429', 'name': '2012', 'params': {}, 'cost': 19500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '656112980286504920', 'name': '3', 'type': 'COACH', 'params': {'seats_count': '88', 'code': '36', 'seatCountWithoutReservation': '155', 'no_seats': '', 'coach_cols': '5', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210061', 'createdDate': '2022-03-02T17:27:21.756+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 3', 'name': 'AC 3', 'key': 'enr_coach_class', 'longV': '10055', 'params': {'pax_class': '55', 'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'sales_tax': '1', 'no_seats': '0', 'templateName': 'ENR Coach Class', 'en': 'AC 3', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'specific_priority': '', 'key': 'enr_coach_class'}, 'implClass': 'java.lang.String', 'seqno': 30, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, 'cost': 19500, 'workOrderId': '986654276340164365', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '19500', 'Specific': '19500', 'No seat': '19500', 'BASE': '19500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '687405386327654366', 'createdDate': '2022-08-10T23:04:15.992+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC3_TD', 'name': 'AC 3', 'key': 'enr_train_description', 'integerV': 25, 'params': {'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'templateName': 'Enr Train Description', 'stand_limitAC 3': '5', 'en': 'AC 3', 'day_limitAC 3': '360', 'used': 'service_point,service,fare', 'support_classes': 'GA 2,AC 3', 'Sheet': 'ImpField', 'stand_limitGA 2': '5', 'day_limitGA 2': '360', 'key': 'enr_train_description'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210061': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230004', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986654276338394893', 'serviceId': '986654276338460429', 'startingPrice': 19500, 'durationInMMHH': '12:50'}], 'classesCostMap': {'1210061': 19500}, 'profileMap': {'1210061': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390819516470', '606535390819516493', '606535391364775989', '606535391364775997', '606535391364776001', '606535391364776008', '606535391364776027', '606535391914229815', '606535391914229829', '606535391914229842', '606535391914229854', '606535392467877944', '606535392467877956'], 'coachWorkOrderId': '986654276340164365'}], 'specialId': '1230004', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 19500, 'distance': 879, 'duration': 770, 'timeFinish': '2024-11-29T06:20:00.000+02:00', 'seatsAvailable': 0, 'timeStart': '2024-11-28T17:30:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 1000, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645592', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645600', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516468', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516477', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516481', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516488', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516502', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516507', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775993', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775995', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776004', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776011', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776013', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776019', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776023', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776025', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776033', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229820', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229822', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229845', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229846', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229849', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877938', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877946', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 0, 'standAvailableSeats': 948, 'fromDate': '2024-11-28T18:00:00.000+02:00', 'finishDate': '2024-11-29T10:40:00.000+02:00', 'duration': 1000, 'train': {'id': '986659780742686477', 'name': '188', 'params': {}, 'cost': 11500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '606535884589760609', 'name': '11', 'type': 'COACH', 'params': {'seats_count': '88', 'code': '36', 'seatCountWithoutReservation': '948', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210059', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'GA 2', 'name': 'GA 2', 'key': 'enr_coach_class', 'longV': '10059', 'params': {'ar': 'ثالثة تهوية', 'en': 'GA 2', 'key': 'enr_coach_class', 'code': 'GA 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '59', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 50, 'localization': '{\"ar\":\"ثالثة تهوية\",\"en\":\"GA 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة تهوية', 'en': 'GA 2'}}, 'cost': 11500, 'workOrderId': '986659780754548493', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '11500', 'Specific': '11500', 'No seat': '11500', 'BASE': '11500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555858002', 'createdDate': '2021-12-30T09:34:40.506+02:00', 'valueType': 'java.lang.String', 'shortName': 'Third Good Air', 'name': 'Third Good Air', 'key': 'enr_train_description', 'integerV': 30, 'params': {'code': 'Third Good Air', 'en': 'Third Good Air', 'used': 'service_point,service,fare', 'voucher_exclude_trains': '', 'stand_limitGA 2': '100', 'day_limitGA 2': '360', 'medals_exclude_trains': '', 'ar': 'ثالثة تهوية', 'suburban_line': 'false', 'templateName': 'Enr Train Description', 'support_classes': 'GA 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': ''}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"ثالثة تهوية\",\"en\":\"Third Good Air\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة تهوية', 'en': 'Third Good Air'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210059': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230004', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986659780742620941', 'serviceId': '986659780742686477', 'startingPrice': 11500, 'durationInMMHH': '16:40'}], 'classesCostMap': {'1210059': 11500}, 'profileMap': {'1210059': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390282645592', '606535390282645600', '606535390819516468', '606535390819516470', '606535390819516477', '606535390819516481', '606535390819516488', '606535390819516493', '606535390819516502', '606535390819516507', '606535391364775989', '606535391364775993', '606535391364775995', '606535391364775997', '606535391364776001', '606535391364776004', '606535391364776008', '606535391364776011', '606535391364776013', '606535391364776015', '606535391364776019', '606535391364776023', '606535391364776025', '606535391364776027', '606535391364776033', '606535391914229815', '606535391914229820', '606535391914229822', '606535391914229829', '606535391914229834', '606535391914229842', '606535391914229845', '606535391914229846', '606535391914229849', '606535391914229854', '606535392467877938', '606535392467877941', '606535392467877944', '606535392467877946', '606535392467877956'], 'coachWorkOrderId': '986659780754548493'}], 'specialId': '1230004', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 11500, 'distance': 879, 'duration': 1000, 'timeFinish': '2024-11-29T10:40:00.000+02:00', 'seatsAvailable': 0, 'timeStart': '2024-11-28T18:00:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 840, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516481', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516484', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516502', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776019', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776023', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776025', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776033', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229822', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877938', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 0, 'standAvailableSeats': 948, 'fromDate': '2024-11-28T18:50:00.000+02:00', 'finishDate': '2024-11-29T08:50:00.000+02:00', 'duration': 840, 'train': {'id': '986674623446460173', 'name': '1014', 'params': {}, 'cost': 11500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '606535884589760609', 'name': '4', 'type': 'COACH', 'params': {'seats_count': '88', 'code': '36', 'seatCountWithoutReservation': '948', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210059', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'GA 2', 'name': 'GA 2', 'key': 'enr_coach_class', 'longV': '10059', 'params': {'ar': 'ثالثة تهوية', 'en': 'GA 2', 'key': 'enr_coach_class', 'code': 'GA 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '59', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 50, 'localization': '{\"ar\":\"ثالثة تهوية\",\"en\":\"GA 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة تهوية', 'en': 'GA 2'}}, 'cost': 11500, 'workOrderId': '986674623520384781', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '11500', 'Specific': '11500', 'No seat': '11500', 'BASE': '11500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555858002', 'createdDate': '2021-12-30T09:34:40.506+02:00', 'valueType': 'java.lang.String', 'shortName': 'Third Good Air', 'name': 'Third Good Air', 'key': 'enr_train_description', 'integerV': 30, 'params': {'code': 'Third Good Air', 'en': 'Third Good Air', 'used': 'service_point,service,fare', 'voucher_exclude_trains': '', 'stand_limitGA 2': '100', 'day_limitGA 2': '360', 'medals_exclude_trains': '', 'ar': 'ثالثة تهوية', 'suburban_line': 'false', 'templateName': 'Enr Train Description', 'support_classes': 'GA 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': ''}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"ثالثة تهوية\",\"en\":\"Third Good Air\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة تهوية', 'en': 'Third Good Air'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210059': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230004', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986674623446394637', 'serviceId': '986674623446460173', 'startingPrice': 11500, 'durationInMMHH': '14:00'}], 'classesCostMap': {'1210059': 11500}, 'profileMap': {'1210059': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390819516470', '606535390819516481', '606535390819516484', '606535390819516493', '606535390819516502', '606535391364775989', '606535391364776001', '606535391364776008', '606535391364776015', '606535391364776019', '606535391364776023', '606535391364776025', '606535391364776027', '606535391364776033', '606535391914229815', '606535391914229822', '606535391914229829', '606535391914229834', '606535391914229842', '606535391914229854', '606535392467877938', '606535392467877941', '606535392467877944', '606535392467877956'], 'coachWorkOrderId': '986674623520384781'}], 'specialId': '1230004', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 11500, 'distance': 879, 'duration': 840, 'timeFinish': '2024-11-29T08:50:00.000+02:00', 'seatsAvailable': 0, 'timeStart': '2024-11-28T18:50:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 700, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 34, 'standAvailableSeats': 0, 'fromDate': '2024-11-28T19:00:00.000+02:00', 'finishDate': '2024-11-29T06:40:00.000+02:00', 'duration': 700, 'train': {'id': '986677214818739981', 'name': '2030', 'params': {}, 'cost': 70000, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '717089183352487917', 'name': '13', 'type': 'COACH', 'params': {'seats_count': '42', 'seatCountWithoutReservation': '0', 'no_seats': 'false', 'sortType': 'bottom', 'passageway': '2', 'coach_rows': '4', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 55000, 'workOrderId': '986677214824376077', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '55000', 'Specific': '55000', 'No seat': '55000', 'BASE': '55000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '717090067323092954', 'name': '5', 'type': 'COACH', 'params': {'seats_count': '27', 'seatCountWithoutReservation': '0', 'no_seats': 'false', 'sortType': 'bottom', 'passageway': '2', 'coach_rows': '3', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 70000, 'workOrderId': '986677214824048397', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '70000', 'Specific': '70000', 'No seat': '70000', 'BASE': '70000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '717088827469987802', 'name': '12', 'type': 'COACH', 'params': {'seats_count': '44', 'no_seats': 'false', 'sortType': 'bottom', 'passageway': '2', 'coach_rows': '4', 'locked': 'true', 'seatCount': '25'}, 'availableSeats': ['717088827469987833', '717088827469987832', '717088827469987835', '717088827469987834', '717088827469987837', '717088827469987836', '717088827469987840', '717088827469987841', '717088827469987809', '717088827469987808', '717088827469987811', '717088827469987813', '717088827469987814', '717088827469987817', '717088827469987816', '717088827469987819', '717088850630934483', '717088827469987820', '717088850630934480', '717088850630934481', '717088827469987822', '717088827469987803', '717088827469987805', '717088827469987807', '717088827469987806'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 55000, 'workOrderId': '986677214824441613', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '55000', 'Specific': '55000', 'No seat': '55000', 'BASE': '55000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '717090067323092954', 'name': '4', 'type': 'COACH', 'params': {'seats_count': '27', 'no_seats': 'false', 'sortType': 'bottom', 'passageway': '2', 'coach_rows': '3', 'locked': 'true', 'seatCount': '9'}, 'availableSeats': ['717090067323092974', '717090067323092959', '717090067323092970', '717090067323092968', '717090067323092969', '717090067323092979', '717090067323092960', '717090067323092976', '717090067323092961'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 70000, 'workOrderId': '986677214824113933', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '70000', 'Specific': '70000', 'No seat': '70000', 'BASE': '70000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '717093494157475895', 'createdDate': '2022-10-31T21:13:35.241+02:00', 'valueType': 'java.lang.String', 'shortName': 'TALGO', 'name': 'TALGO', 'key': 'enr_train_description', 'integerV': 22, 'params': {'code': 'TALGO', 'used': 'service_point,service,fare', 'day_limitAC 2': '360', 'day_limitAC 1': '360', 'voucher_exclude_trains': '', 'medals_exclude_trains': '2', 'suburban_line': 'false', 'templateName': 'Enr Train Description', 'stand_limitAC 1': '0', 'stand_limitAC 2': '0', 'support_classes': 'AC 1,AC 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': '2'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"تالجو\",\"en\":\"TALGO\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'تالجو', 'en': 'TALGO'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '752454808710938582', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986677214818674445', 'serviceId': '986677214818739981', 'startingPrice': 55000, 'durationInMMHH': '11:40'}], 'classesCostMap': {'1210000': 70000, '1210001': 55000}, 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390819516493', '606535391364775989', '606535391364776008', '606535391914229815', '606535391914229829', '606535391914229854', '606535392467877944', '606535392467877956'], 'coachWorkOrderId': '986677214824048397'}], 'specialId': '752454808710938582', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 55000, 'distance': 879, 'duration': 700, 'timeFinish': '2024-11-29T06:40:00.000+02:00', 'seatsAvailable': 34, 'timeStart': '2024-11-28T19:00:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 760, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877946', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 0, 'standAvailableSeats': 155, 'fromDate': '2024-11-28T19:10:00.000+02:00', 'finishDate': '2024-11-29T07:50:00.000+02:00', 'duration': 760, 'train': {'id': '986679249535379213', 'name': '988', 'params': {}, 'cost': 19500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '656112980286504920', 'name': '7', 'type': 'COACH', 'params': {'seats_count': '88', 'code': '36', 'seatCountWithoutReservation': '155', 'no_seats': '', 'coach_cols': '5', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210061', 'createdDate': '2022-03-02T17:27:21.756+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 3', 'name': 'AC 3', 'key': 'enr_coach_class', 'longV': '10055', 'params': {'pax_class': '55', 'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'sales_tax': '1', 'no_seats': '0', 'templateName': 'ENR Coach Class', 'en': 'AC 3', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'specific_priority': '', 'key': 'enr_coach_class'}, 'implClass': 'java.lang.String', 'seqno': 30, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, 'cost': 19500, 'workOrderId': '986679249536558861', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '19500', 'Specific': '19500', 'No seat': '19500', 'BASE': '19500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '687405386327654366', 'createdDate': '2022-08-10T23:04:15.992+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC3_TD', 'name': 'AC 3', 'key': 'enr_train_description', 'integerV': 25, 'params': {'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'templateName': 'Enr Train Description', 'stand_limitAC 3': '5', 'en': 'AC 3', 'day_limitAC 3': '360', 'used': 'service_point,service,fare', 'support_classes': 'GA 2,AC 3', 'Sheet': 'ImpField', 'stand_limitGA 2': '5', 'day_limitGA 2': '360', 'key': 'enr_train_description'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210061': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230004', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986679249535313677', 'serviceId': '986679249535379213', 'startingPrice': 19500, 'durationInMMHH': '12:40'}], 'classesCostMap': {'1210061': 19500}, 'profileMap': {'1210061': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390819516470', '606535390819516493', '606535391364775989', '606535391364776008', '606535391364776015', '606535391364776027', '606535391914229815', '606535391914229829', '606535391914229842', '606535391914229854', '606535392467877944', '606535392467877946', '606535392467877956'], 'coachWorkOrderId': '986679249536558861'}], 'specialId': '1230004', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 19500, 'distance': 879, 'duration': 760, 'timeFinish': '2024-11-29T07:50:00.000+02:00', 'seatsAvailable': 0, 'timeStart': '2024-11-28T19:10:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 895, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645600', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516481', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516484', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516502', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516507', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775993', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776004', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776019', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776025', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229822', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 0, 'standAvailableSeats': 176, 'fromDate': '2024-11-28T20:20:00.000+02:00', 'finishDate': '2024-11-29T11:15:00.000+02:00', 'duration': 895, 'train': {'id': '986697283551110925', 'name': '1012', 'params': {}, 'cost': 19500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '656112980286504920', 'name': '1', 'type': 'COACH', 'params': {'seats_count': '88', 'code': '36', 'seatCountWithoutReservation': '176', 'no_seats': '', 'coach_cols': '5', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210061', 'createdDate': '2022-03-02T17:27:21.756+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 3', 'name': 'AC 3', 'key': 'enr_coach_class', 'longV': '10055', 'params': {'pax_class': '55', 'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'sales_tax': '1', 'no_seats': '0', 'templateName': 'ENR Coach Class', 'en': 'AC 3', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'specific_priority': '', 'key': 'enr_coach_class'}, 'implClass': 'java.lang.String', 'seqno': 30, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, 'cost': 19500, 'workOrderId': '986697283633030925', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '19500', 'Specific': '19500', 'No seat': '19500', 'BASE': '19500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '687405386327654366', 'createdDate': '2022-08-10T23:04:15.992+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC3_TD', 'name': 'AC 3', 'key': 'enr_train_description', 'integerV': 25, 'params': {'ar': 'ثالثة مكيفة', 'code': 'AC 3', 'templateName': 'Enr Train Description', 'stand_limitAC 3': '5', 'en': 'AC 3', 'day_limitAC 3': '360', 'used': 'service_point,service,fare', 'support_classes': 'GA 2,AC 3', 'Sheet': 'ImpField', 'stand_limitGA 2': '5', 'day_limitGA 2': '360', 'key': 'enr_train_description'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"ثالثة مكيفة\",\"en\":\"AC 3\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة مكيفة', 'en': 'AC 3'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210061': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230004', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986697283551045389', 'serviceId': '986697283551110925', 'startingPrice': 19500, 'durationInMMHH': '14:55'}], 'classesCostMap': {'1210061': 19500}, 'profileMap': {'1210061': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390282645600', '606535390819516470', '606535390819516481', '606535390819516484', '606535390819516493', '606535390819516502', '606535390819516507', '606535391364775989', '606535391364775993', '606535391364775997', '606535391364776001', '606535391364776004', '606535391364776008', '606535391364776015', '606535391364776019', '606535391364776025', '606535391364776027', '606535391914229815', '606535391914229822', '606535391914229829', '606535391914229834', '606535391914229842', '606535391914229854', '606535392467877941', '606535392467877944', '606535392467877956'], 'coachWorkOrderId': '986697283633030925'}], 'specialId': '1230004', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 19500, 'distance': 879, 'duration': 895, 'timeFinish': '2024-11-29T11:15:00.000+02:00', 'seatsAvailable': 0, 'timeStart': '2024-11-28T20:20:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 760, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 60, 'standAvailableSeats': 27, 'fromDate': '2024-11-28T21:00:00.000+02:00', 'finishDate': '2024-11-29T09:40:00.000+02:00', 'duration': 760, 'train': {'id': '986704962059183885', 'name': '2014', 'params': {}, 'cost': 24500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '950753799231964133', 'name': '6', 'type': 'COACH', 'params': {'seats_count': '60', 'seatCountWithoutReservation': '19', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535876171792444', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986704962077271821', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950751353545491429', 'name': '1', 'type': 'COACH', 'params': {'seats_count': '44', 'seatCountWithoutReservation': '8', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535878239584314', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986704962077402893', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950753799231964133', 'name': '9', 'type': 'COACH', 'params': {'seats_count': '60', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535876171792444', 'seatCount': '12'}, 'availableSeats': ['950753799232750565', '950753799232685029', '950753799232619493', '950753799232553957', '950753799232488421', '950753799232422885', '950753799232357349', '950753799232291813', '950753799232226277', '950753799232160741', '950753799232095205', '950753799232029669'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986704962077206285', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535878239584314', 'name': '2', 'type': 'COACH', 'params': {'seats_count': '44', 'code': '15', 'no_seats': '', 'coach_cols': '4', 'coach_rows': '3', 'locked': 'true', 'seatCount': '2'}, 'availableSeats': ['606535878642237492', '606535878239584330'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986704962081793805', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950751353545491429', 'name': '3', 'type': 'COACH', 'params': {'seats_count': '44', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535878239584314', 'seatCount': '15'}, 'availableSeats': ['950751353545622501', '950751353545556965', '950751353546015717', '950751353545950181', '950751353546146789', '950751353546081253', '950751353545753573', '950751353545688037', '950751353545884645', '950751353545819109', '950751353546540005', '950751353546277861', '950751353546212325', '950751353546408933', '950751353546343397'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986704962077337357', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535876171792444', 'name': '8', 'type': 'COACH', 'params': {'seats_count': '60', 'code': '6', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '31'}, 'availableSeats': ['606535876171792448', '606535876599611456', '606535876171792449', '606535876599611457', '606535876171792450', '606535876599611458', '606535876171792451', '606535876599611459', '606535876171792452', '606535876599611460', '606535876171792453', '606535876599611461', '606535876171792454', '606535876599611462', '606535876171792456', '606535876171792457', '606535876171792458', '606535876171792459', '606535876171792460', '606535876171792461', '606535876171792462', '606535876171792463', '606535876171792465', '606535876171792468', '606535876171792469', '606535876171792470', '606535876171792471', '606535876171792472', '606535876171792474', '606535876171792475', '606535876171792481'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986704962082055949', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555857991', 'createdDate': '2021-12-30T09:34:40.480+02:00', 'valueType': 'java.lang.String', 'shortName': 'Special', 'name': 'Special', 'key': 'enr_train_description', 'integerV': 10, 'params': {'code': 'Special', 'en': 'Special', 'used': 'service_point,service,fare', 'day_limitAC 2': '360', 'day_limitAC 1': '360', 'voucher_exclude_trains': '', 'medals_exclude_trains': '2', 'ar': 'خاص', 'templateName': 'Enr Train Description', 'stand_limitAC 1': '5', 'stand_limitAC 2': '5', 'sourceAC 2': \"'quota'\", 'sourceAC 1': \"'quota'\", 'support_classes': 'AC 1,AC 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': '2'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"خاص\",\"en\":\"Special\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'خاص', 'en': 'Special'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230005', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986704962059118349', 'serviceId': '986704962059183885', 'startingPrice': 24500, 'durationInMMHH': '12:40'}], 'classesCostMap': {'1210000': 38000, '1210001': 24500}, 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390819516493', '606535391364775989', '606535391364776008', '606535391364776027', '606535391914229815', '606535391914229829', '606535391914229842', '606535391914229854', '606535392467877944', '606535392467877956'], 'coachWorkOrderId': '986704962077402893'}], 'specialId': '1230005', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 24500, 'distance': 879, 'duration': 760, 'timeFinish': '2024-11-29T09:40:00.000+02:00', 'seatsAvailable': 60, 'timeStart': '2024-11-28T21:00:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 810, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 74, 'standAvailableSeats': 96, 'fromDate': '2024-11-28T22:00:00.000+02:00', 'finishDate': '2024-11-29T11:30:00.000+02:00', 'duration': 810, 'train': {'id': '986720089628223245', 'name': '996', 'params': {}, 'cost': 24500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '606535875345514549', 'name': '2', 'type': 'COACH', 'params': {'seats_count': '47', 'code': '4', 'no_seats': '', 'coach_cols': '4', 'coach_rows': '3', 'locked': 'true', 'seatCount': '3'}, 'availableSeats': ['606535875345514551', '606535875345514568', '606535875345514570'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986722409278220045', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950753799288521701', 'name': '3', 'type': 'COACH', 'params': {'seats_count': '47', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535875345514549', 'seatCount': '18'}, 'availableSeats': ['950753799289373669', '950753799289308133', '950753799289242597', '950753799289177061', '950753799289111525', '950753799289045989', '950753799288980453', '950753799288914917', '950753799288849381', '950753799288783845', '950753799288718309', '950753799288652773', '950753799288587237', '950753799293895653', '950753799293830117', '950753799293764581', '950753799293699045', '950753799293633509'], 'coachClass': {'id': '1210000', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 1', 'name': 'AC 1', 'key': 'enr_coach_class', 'longV': '10000', 'params': {'ar': 'أولى مكيفة', 'en': 'AC 1', 'key': 'enr_coach_class', 'code': 'AC 1', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '0', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 10, 'localization': '{\"ar\":\"أولى مكيفة\",\"en\":\"AC 1\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'أولى مكيفة', 'en': 'AC 1'}}, 'cost': 38000, 'workOrderId': '986722409277761293', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '38000', 'Specific': '47500', 'No seat': '38000', 'BASE': '38000'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950753034885661669', 'name': '6', 'type': 'COACH', 'params': {'seats_count': '64', 'seatCountWithoutReservation': '96', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535876599611464', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986722409277499149', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '606535876599611464', 'name': '8', 'type': 'COACH', 'params': {'seats_count': '64', 'code': '7', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '11'}, 'availableSeats': ['606535877019041856', '606535877019041858', '606535877019041859', '606535877019041864', '606535876599611466', '606535877019041866', '606535876599611467', '606535877019041867', '606535877019041868', '606535876599611469', '606535877019041869'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986722409278023437', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}, {'id': '950753034885661669', 'name': '9', 'type': 'COACH', 'params': {'seats_count': '64', 'no_seats': 'false', 'locked': 'true', 'cloned_from': '606535876599611464', 'seatCount': '42'}, 'availableSeats': ['950753034890970085', '950753034890904549', '950753034891035621', '950753034890707941', '950753034890642405', '950753034890839013', '950753034890773477', '950753034890445797', '950753034890380261', '950753034890576869', '950753034890511333', '950753034890183653', '950753034890118117', '950753034890314725', '950753034890249189', '950753034891232229', '950753034891166693', '950753034891297765', '950753034893067237', '950753034893001701', '950753034893198309', '950753034893132773', '950753034892805093', '950753034892739557', '950753034892936165', '950753034892870629', '950753034892542949', '950753034892477413', '950753034892674021', '950753034892608485', '950753034892280805', '950753034892411877', '950753034892346341', '950753034885858277', '950753034885792741', '950753034893591525', '950753034893525989', '950753034893657061', '950753034893329381', '950753034893263845', '950753034893460453', '950753034893394917'], 'coachClass': {'id': '1210001', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'AC 2', 'name': 'AC 2', 'key': 'enr_coach_class', 'longV': '10001', 'params': {'ar': 'ثانية مكيفة', 'en': 'AC 2', 'key': 'enr_coach_class', 'code': 'AC 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '1', 'sales_tax': '1', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 20, 'localization': '{\"ar\":\"ثانية مكيفة\",\"en\":\"AC 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثانية مكيفة', 'en': 'AC 2'}}, 'cost': 24500, 'workOrderId': '986722409277695757', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '24500', 'Specific': '31000', 'No seat': '24500', 'BASE': '24500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555857991', 'createdDate': '2021-12-30T09:34:40.480+02:00', 'valueType': 'java.lang.String', 'shortName': 'Special', 'name': 'Special', 'key': 'enr_train_description', 'integerV': 10, 'params': {'code': 'Special', 'en': 'Special', 'used': 'service_point,service,fare', 'day_limitAC 2': '360', 'day_limitAC 1': '360', 'voucher_exclude_trains': '', 'medals_exclude_trains': '2', 'ar': 'خاص', 'templateName': 'Enr Train Description', 'stand_limitAC 1': '5', 'stand_limitAC 2': '5', 'sourceAC 2': \"'quota'\", 'sourceAC 1': \"'quota'\", 'support_classes': 'AC 1,AC 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': '2'}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"خاص\",\"en\":\"Special\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'خاص', 'en': 'Special'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230005', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986720089628157709', 'serviceId': '986720089628223245', 'startingPrice': 24500, 'durationInMMHH': '13:30'}], 'classesCostMap': {'1210000': 38000, '1210001': 24500}, 'profileMap': {'1210000': ['1245015'], '1210001': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390819516470', '606535390819516493', '606535391364775989', '606535391364776008', '606535391364776027', '606535391914229815', '606535391914229829', '606535391914229842', '606535391914229854', '606535392467877941', '606535392467877944', '606535392467877956'], 'coachWorkOrderId': '986722409278220045'}], 'specialId': '1230005', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 24500, 'distance': 879, 'duration': 810, 'timeFinish': '2024-11-29T11:30:00.000+02:00', 'seatsAvailable': 74, 'timeStart': '2024-11-28T22:00:00.000+02:00'}, {'steps': [{'availableQuota': False, 'sequenceNumber': 0, 'totalDistance': 879, 'totalOffsetStart': 0, 'totalOffsetFinish': 900, 'currency': 'EGP', 'route': [{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645600', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516477', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516481', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516502', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516507', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775993', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775995', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776004', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776011', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776013', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776019', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776023', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776025', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776033', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229820', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229822', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229845', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229846', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229849', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877938', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877946', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877948', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}], 'availableSeats': 0, 'standAvailableSeats': 3460, 'fromDate': '2024-11-28T22:10:00.000+02:00', 'finishDate': '2024-11-29T13:10:00.000+02:00', 'duration': 900, 'train': {'id': '986722475338180365', 'name': '90', 'params': {}, 'cost': 11500, 'distanceForCalc': 0, 'seatTypeCosts': {}, 'profileClassesAvailable': [], 'localizationMap': {}, 'servicePoints': [{'id': '606535884589760609', 'name': '9', 'type': 'COACH', 'params': {'seats_count': '88', 'code': '36', 'seatCountWithoutReservation': '3460', 'no_seats': '', 'coach_cols': '5', 'coach_rows': '4', 'locked': 'true', 'seatCount': '0'}, 'availableSeats': [], 'coachClass': {'id': '1210059', 'createdDate': '2021-12-21T16:49:10.809+02:00', 'valueType': 'java.lang.String', 'shortName': 'GA 2', 'name': 'GA 2', 'key': 'enr_coach_class', 'longV': '10059', 'params': {'ar': 'ثالثة تهوية', 'en': 'GA 2', 'key': 'enr_coach_class', 'code': 'GA 2', 'used': 'fare,service_point', 'Sheet': 'ImpField', 'no_seats': '0', 'pax_class': '59', 'templateName': 'ENR Coach Class'}, 'implClass': 'java.lang.String', 'seqno': 50, 'localization': '{\"ar\":\"ثالثة تهوية\",\"en\":\"GA 2\"}', 'leaf': True, 'templateId': '1200000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة تهوية', 'en': 'GA 2'}}, 'cost': 11500, 'workOrderId': '986725013774542605', 'distanceForCalc': 879, 'seatTypeCosts': {'Seat Type': '11500', 'Specific': '11500', 'No seat': '11500', 'BASE': '11500'}, 'profileClassesAvailable': ['1245015'], 'localizationMap': {}, 'places': []}], 'fields': [{'id': '606388820555858002', 'createdDate': '2021-12-30T09:34:40.506+02:00', 'valueType': 'java.lang.String', 'shortName': 'Third Good Air', 'name': 'Third Good Air', 'key': 'enr_train_description', 'integerV': 30, 'params': {'code': 'Third Good Air', 'en': 'Third Good Air', 'used': 'service_point,service,fare', 'voucher_exclude_trains': '', 'stand_limitGA 2': '100', 'day_limitGA 2': '360', 'medals_exclude_trains': '', 'ar': 'ثالثة تهوية', 'suburban_line': 'false', 'templateName': 'Enr Train Description', 'support_classes': 'GA 2', 'Sheet': 'ImpField', 'key': 'enr_train_description', 'subsc_exclude_trains': ''}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"ثالثة تهوية\",\"en\":\"Third Good Air\"}', 'leaf': True, 'templateId': '606388820555857989', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'ثالثة تهوية', 'en': 'Third Good Air'}}, {'id': '1221001', 'createdDate': '2021-12-21T16:49:11.211+02:00', 'valueType': 'java.lang.String', 'shortName': 'PLD', 'name': 'PLD', 'key': 'enr_train_type', 'stringV': 'PLD', 'params': {}, 'implClass': 'java.lang.String', 'seqno': 0, 'localization': '{\"ar\":\"قطاع المسافات الطويلة\",\"en\":\"PLD\"}', 'leaf': True, 'templateId': '1221000', 'onlyRestructureChild': False, 'needUpgrade': False, 'localizationMap': {'ar': 'قطاع المسافات الطويلة', 'en': 'PLD'}}]}, 'profileClassesAvailable': ['1245015'], 'profileMap': {'1210059': ['1245015']}, 'noSeats': False, 'faresMap': {}, 'extReserve': False, 'specificId': '1230004', 'fromId': 606534187276566625, 'toId': 606535392467877956, 'from': {'id': '606534187276566625', 'createdDate': '2021-12-30T20:10:19.658+02:00', 'shortName': 'ENR_1', 'name': 'CAIRO', 'latlon': {'x': 0.0, 'y': 0.0}, 'address': '', 'placeId': '', 'description': '108', 'type': 0, 'seqno': 0, 'active': True, 'params': {'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'used_in_obs': '1', 'station_code': '108', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"القاهره\",\"en\":\"CAIRO\"}', 'localizationMap': {'ar': 'القاهره', 'en': 'CAIRO'}}, 'to': {'id': '606535392467877956', 'createdDate': '2021-12-30T19:15:06.494+02:00', 'shortName': 'ENR_819', 'name': 'ASWAN', 'address': '', 'placeId': '', 'description': '45829', 'type': 0, 'seqno': 0, 'active': True, 'params': {'offline': 'false', 'has_gates': 'false', 'timezone': 'Africa/Cairo', 'used_in_agent': '1', 'afterDepartureTime': '0', 'used_in_obs': '1', 'beforeDepartureTime': '0', 'station_code': '45829', 'time_zone': '1', 'used_in_train': '1'}, 'localization': '{\"ar\":\"اسوان\",\"en\":\"ASWAN\"}', 'extId': '', 'localizationMap': {'ar': 'اسوان', 'en': 'ASWAN'}}, 'workOrderId': '986722475338114829', 'serviceId': '986722475338180365', 'startingPrice': 11500, 'durationInMMHH': '15:00'}], 'classesCostMap': {'1210059': 11500}, 'profileMap': {'1210059': ['1245015']}, 'ticketBuyRequestTemplate': {'waiting': False, 'auto': False, 'steps': [{'tripPoints': ['606534187276566625', '606535390282645575', '606535390282645600', '606535390819516470', '606535390819516477', '606535390819516481', '606535390819516493', '606535390819516502', '606535390819516507', '606535391364775989', '606535391364775993', '606535391364775995', '606535391364775997', '606535391364776001', '606535391364776004', '606535391364776008', '606535391364776011', '606535391364776013', '606535391364776015', '606535391364776019', '606535391364776023', '606535391364776025', '606535391364776027', '606535391364776033', '606535391914229815', '606535391914229820', '606535391914229822', '606535391914229829', '606535391914229834', '606535391914229842', '606535391914229845', '606535391914229846', '606535391914229849', '606535391914229854', '606535392467877938', '606535392467877941', '606535392467877944', '606535392467877946', '606535392467877948', '606535392467877956'], 'coachWorkOrderId': '986725013774542605'}], 'specialId': '1230004', 'withoutSeat': False, 'dailyReturn': 'one_way', 'needUpgrade': False, 'offline': False, 'guide': False}, 'currency': 'EGP', 'startingPrice': 11500, 'distance': 879, 'duration': 900, 'timeFinish': '2024-11-29T13:10:00.000+02:00', 'seatsAvailable': 0, 'timeStart': '2024-11-28T22:10:00.000+02:00'}]\n"]}], "source": ["print(data)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["dict_keys(['steps', 'classesCostMap', 'profileMap', 'ticketBuyRequestTemplate', 'currency', 'startingPrice', 'distance', 'duration', 'timeFinish', 'seatsAvailable', 'timeStart'])\n"]}], "source": ["print(data[0].keys())"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'id': '606534187276566625', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645575', 'params': {}, 'localizationMap': {}}, {'id': '606535390282645600', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516470', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516481', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516493', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516502', 'params': {}, 'localizationMap': {}}, {'id': '606535390819516507', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775989', 'params': {}, 'localizationMap': {}}, {'id': '606535391364775997', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776001', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776008', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776015', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776019', 'params': {}, 'localizationMap': {}}, {'id': '606535391364776027', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229815', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229822', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229829', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229834', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229842', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229846', 'params': {}, 'localizationMap': {}}, {'id': '606535391914229854', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877938', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877941', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877944', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877946', 'params': {}, 'localizationMap': {}}, {'id': '606535392467877956', 'params': {}, 'localizationMap': {}}]\n"]}], "source": ["print(data[0]['steps'][0]['route'])"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["960826024954045114\n"]}], "source": ["print(data[4]['steps'][0]['specificId'])"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n", "1\n"]}], "source": ["# print the number of steps in every ticket\n", "for ticket in data:\n", "    print(len(ticket['steps']))\n", "    "]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}