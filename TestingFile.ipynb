{"cells": [{"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["response Time 4.485037 Trips found 36 id 2\n", "response Time 3.227019 Trips found 0 id 3\n", "response Time 3.947054 Trips found 203 id 4\n", "response Time 3.448314 Trips found 20 id 5\n", "response Time 3.169523 Trips found 31 id 6\n", "response Time 3.171202 Trips found 4 id 7\n", "response Time 3.17283 Trips found 10 id 8\n", "response Time 3.862546 Trips found 169 id 9\n", "response Time 3.423754 Trips found 9 id 10\n", "response Time 3.497276 Trips found 5 id 11\n", "response Time 3.178816 Trips found 0 id 12\n", "response Time 2.979551 Trips found 0 id 13\n", "response Time 3.300545 Trips found 1 id 14\n", "response Time 3.21572 Trips found 19 id 15\n", "response Time 3.134514 Trips found 1 id 16\n", "response Time 3.837263 Trips found 0 id 17\n", "response Time 3.097837 Trips found 0 id 18\n", "response Time 3.415223 Trips found 66 id 19\n", "response Time 3.06237 Trips found 15 id 20\n", "response Time 0.465015 Trips found 108 id 21\n", "response Time 0.406451 Trips found 0 id 22\n", "response Time 0.391924 Trips found 0 id 23\n", "response Time 4.435338 Trips found 50 id 24\n", "response Time 8.430841 Trips found 29 id 25\n", "response Time 0.411961 Trips found 0 id 26\n", "response Time 4.474178 Trips found 0 id 27\n", "response Time 4.436561 Trips found 27 id 28\n", "response Time 4.408549 Trips found 36 id 29\n", "response Time 8.425098 Trips found 0 id 30\n", "response Time 5.170925 Trips found 0 id 31\n", "response Time 4.417848 Trips found 30 id 32\n", "response Time 4.500351 Trips found 0 id 33\n", "response Time 0.484327 Trips found 0 id 34\n", "response Time 0.350462 Trips found 0 id 35\n", "response Time 0.326217 Trips found 0 id 36\n", "response Time 0.333518 Trips found 0 id 37\n", "response Time 0.38453 Trips found 0 id 38\n", "response Time 0.333015 Trips found 0 id 39\n"]}], "source": ["\n", "import requests\n", "import json\n", "\n", "url = \"http://localhost:3000/companies/getTrips\"\n", "for i in range(2, 40):\n", "    payload = json.dumps({\n", "        \"from\": 1,\n", "        \"to\": i,\n", "        \"date\": \"2024-09-15\",\n", "        \"seatsNo\": -1\n", "    })\n", "    headers = {\n", "        'Content-Type': 'application/json'\n", "    }\n", "\n", "    response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "\n", "    results = response.json()\n", "    results = results['trips']\n", "    print(  \"response Time\" , response.elapsed.total_seconds() ,\"Trips found\" , len(results) , \"id\" , i)\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["response Time 4.660685 Trips found 203\n"]}], "source": ["\n", "import requests\n", "import json\n", "\n", "url = \"http://localhost:3000/companies/getTrips\"\n", "\n", "payload = json.dumps({\n", "    \"from\": 1,\n", "    \"to\": 4,\n", "    \"date\": \"2024-09-15\",\n", "    \"seatsNo\": -1\n", "})\n", "headers = {\n", "    'Content-Type': 'application/json'\n", "}\n", "\n", "response = requests.request(\"POST\", url, headers=headers, data=payload)\n", "\n", "results = response.json()\n", "results = results['trips']\n", "# print(len(results))\n", "# add response time\n", "print(  \"response Time\" , response.elapsed.total_seconds() ,\"Trips found\" , len(results) )\n", "\n"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "Porto El Sokhna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "Porto El Sokhna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "Porto El Sokhna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "Porto El Sokhna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "Porto El Sokhna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "Porto El Sokhna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "Porto El Sokhna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "Porto El Sokhna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "Porto El Sokhna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "<PERSON><PERSON>\n", "<PERSON><PERSON><PERSON><PERSON>\n", "مكتب الأحياء GoBus\n", "<PERSON><PERSON>\n", "El Gouna GoBus\n", "<PERSON><PERSON><PERSON><PERSON>\n", "Hurghada WE-BUS\n", "Hurghada WE-BUS\n", "Hurghada WE-BUS\n"]}], "source": ["for i in results:\n", "\n", "    print(i['destination_city'] , i['company_name'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}