{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "\n", "data = [\n", "    {\n", "        \"companyName\": \"blueBus\",\n", "        \"category\": \"canCategory\",\n", "        \"text_en\": \"In case of cancellation of the ticket within four hours before the date of the trip, 50% of the ticket value will be deducted as administrative fees.\",\n", "        \"text_ar\": \"في حالة إلغاء التذكرة خلال الأربع ساعات التي تسبق موعد الرحلة، سيتم خصم 50٪ من قيمة التذكرة كرسوم إدارية.\",\n", "        \"serviceId\": \"1f72d090-2eb6-451d-831f-a9777fd690f1\",\n", "        \"amount\": 50,\n", "        \"amountType\": \"percentage\",\n", "        \"duration\": \"240\",\n", "        \"isDefault\": 1\n", "    },\n", "    {\n", "        \"companyName\": \"blueBus\",\n", "        \"category\": \"canCategory\",\n", "        \"text_en\": \"No cancellation or modification of the ticket is permitted two hours before the trip time written on the ticket.\",\n", "        \"text_ar\": \"لا يُسمح بإلغاء أو تعديل التذكرة قبل ساعتين من موعد الرحلة المكتوب على التذكرة.\",\n", "        \"serviceId\": \"1f72d090-2eb6-451d-831f-a9777fd690f1\",\n", "        \"amount\": 0,\n", "        \"amountType\": \"percentage\",\n", "        \"duration\": \"120\", \n", "        \"isDefault\": 0\n", "    }\n", "]\n", "\n", "url = \"http://localhost:3010/tr/admin/cancelation/addCancelation\"\n", "\n", "for item in data:\n", "    response = requests.post(url, json=item)\n", "    if response.status_code == 200:\n", "        print(f\"Successfully sent: {item}\")\n", "    else:\n", "        print(f\"Failed to send: {item}, Status code: {response.status_code}, Response: {response.text}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"name": "python", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}