{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["From City ID: \n"]}], "source": ["import requests\n", "\n", "response = requests.get('https://gobus.me/api/V3/GetTripsByCity?FromCityId=2&ToCityId=4&TripDate=22%2f06%2f2025&token=goBusv3')\n", "\n", "print(\"From City ID:\", response.status_code)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}