{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# User name,Password,Console sign-in URL\n", "# dev_secrets,trego_dev@2207,https://869935092981.signin.aws.amazon.com/console"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# # Use this code snippet in your app.\n", "# # If you need more information about configurations\n", "# # or implementing the sample code, visit the AWS docs:\n", "# # https://aws.amazon.com/developer/language/python/\n", "\n", "# import boto3\n", "# from botocore.exceptions import ClientError\n", "\n", "\n", "# def get_secret():\n", "\n", "#     secret_name = \"databaseCred\"\n", "#     region_name = \"eu-central-1\"\n", "\n", "#     # Create a Secrets Manager client\n", "#     session = boto3.session.Session()\n", "#     client = session.client(\n", "#         service_name='secretsmanager',\n", "#         region_name=region_name\n", "#     )\n", "\n", "#     try:\n", "#         get_secret_value_response = client.get_secret_value(\n", "#             SecretId=secret_name\n", "#         )\n", "#     except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "#         # For a list of exceptions thrown, see\n", "#         # https://docs.aws.amazon.com/secretsmanager/latest/apireference/API_GetSecretValue.html\n", "#         raise e\n", "\n", "#     secret = get_secret_value_response['SecretString']\n", "\n", "#     # Your code goes here.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'boto3'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[2], line 2\u001b[0m\n\u001b[0;32m      1\u001b[0m \u001b[38;5;66;03m# retrerive the secrets from the AWS Secrets Manager\u001b[39;00m\n\u001b[1;32m----> 2\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON>3\u001b[39;00m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01mi<PERSON>rt\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mos\u001b[39;00m\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'boto3'"]}], "source": ["# retrerive the secrets from the AWS Secrets Manager\n", "import boto3\n", "import json\n", "import os\n", "import logging\n", "from botocore.exceptions import ClientError\n", "\n", "logger = logging.getLogger()\n", "logger.setLevel(logging.INFO)\n", "\n", "def get_secret(secret_name):\n", "    # Create a Secrets Manager client\n", "    session = boto3.session.Session()\n", "    client = session.client(\n", "        service_name='secretsmanager',\n", "        region_name='eu-central-1'\n", "    )\n", "\n", "    try:\n", "        get_secret_value_response = client.get_secret_value(\n", "            SecretId=secret_name\n", "        )\n", "    except <PERSON><PERSON><PERSON><PERSON><PERSON> as e:\n", "        if e.response['Error']['Code'] == 'ResourceNotFoundException':\n", "            logger.error(\"The requested secret \" + secret_name + \" was not found\")\n", "        elif e.response['Error']['Code'] == 'InvalidRequestException':\n", "            logger.error(\"The request was invalid due to:\", e)\n", "        elif e.response['Error']['Code'] == 'InvalidParameterException':\n", "            logger.error(\"The request had invalid params:\", e)\n", "        raise e\n", "    else:\n", "        # Depending on whether the secret is a string or binary, one of these fields will be populated.\n", "        if 'SecretString' in get_secret_value_response:\n", "            secret = get_secret_value_response['SecretString']\n", "        else:\n", "            secret = base64.b64decode(get_secret_value_response['SecretBinary'])\n", "        return secret\n", "    \n", "def get_secret_json(secret_name):\n", "    secret = get_secret(secret_name)\n", "    return json.loads(secret)\n", "\n", "def get_secret_key(secret_name, key):\n", "    secret = get_secret(secret_name)\n", "    return json.loads(secret)[key]\n", "\n", "def get_secret_key_value(secret_name, key):\n", "    secret = get_secret(secret_name)\n", "    return json.loads(secret)[key]\n", "\n", "def get_secret_key_value_json(secret_name, key):\n", "    secret = get_secret(secret_name)\n", "    return json.loads(secret)[key]\n", "\n", "\n", "\n", "# secret_name\n", "# secret_name = \"databaseCred\"\n", "\n", "# # get the secret\n", "\n", "databaseCred = get_secret_json(\"databaseCred\")\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 2}