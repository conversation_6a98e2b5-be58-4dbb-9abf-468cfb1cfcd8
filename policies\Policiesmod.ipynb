import json
from googletrans import Translator
import time

# Load the JSON file
with open('d:/Amr/Safarny/Dev ops/Testing/termsData.json', 'r', encoding='utf-8') as file:
    policies_data = json.load(file)

# Initialize the translator
translator = Translator()

def translate_to_arabic(text, retries=3):
    for attempt in range(retries):
        try:
            translated = translator.translate(text, dest='ar').text
            if translated:
                return translated
        except Exception as e:
            print(f"Error translating text: {text}. Attempt {attempt + 1} of {retries}. Error: {e}")
            time.sleep(1)
    return text 

# Translate the JSON content
translated_policies = []
for section in policies_data:
    translated_section = {}
    for key, value in section.items():
        if isinstance(value, list):
            translated_section[key] = [translate_to_arabic(item) for item in value]
        else:
            translated_section[key] = translate_to_arabic(value)
        # Adding a small delay to avoid hitting the rate limit
        time.sleep(0.5)
    translated_policies.append(translated_section)

# Print the translated JSON
print(json.dumps(translated_policies, ensure_ascii=False, indent=4))

# save the translated JSON to a file
with open('d:/Amr/Safarny/Dev ops/Testing/TermsData_arabic.json', 'w', encoding='utf-8') as file:
    json.dump(translated_policies, file, ensure_ascii=False, indent=4)

# https://api.tregotech.com/tr/admin/terms/insertTerms

import json

# Load the JSON files
with open('d:/Amr/Safarny/Dev ops/Testing/Policies/BlueBus policies.json', 'r', encoding='utf-8') as file_en:
    policies_data_en = json.load(file_en)

with open('d:/Amr/Safarny/Dev ops/Testing/Policies/BlueBus Arabic.json', 'r', encoding='utf-8') as file_ar:
    policies_data_ar = json.load(file_ar)

# Function to process sections and create the desired output format
def process_sections(sections_en, sections_ar):
    processed_data = []
    for section_en, section_ar in zip(sections_en, sections_ar):
        # Extract titles and subtitles
        title_ar = section_ar.get("title", None)
        title_en = section_en.get("title", None)
        subtitle_ar = section_ar.get("subtitle", None)
        subtitle_en = section_en.get("subtitle", None)

        # Initialize a dictionary for the processed section
        processed_section = {
            "title_ar": title_ar,
            "title_en": title_en,
            "subtitle_ar": subtitle_ar,
            "subtitle_en": subtitle_en,
            "descriptions_ar": [],
            "descriptions_en": [],
            "type": "normal",
            "companyName":"blueBus",
            "serviceId":"1f72d090-2eb6-451d-831f-a9777fd690f1"
        }

        # Process paragraphs
        if 'paragraphs' in section_en:
            processed_section["descriptions_ar"] = section_ar.get("paragraphs", [])
            processed_section["descriptions_en"] = section_en.get("paragraphs", [])

            processed_data.append(processed_section)

     
            if 'bullets' in section_en:
                # Append a separate entry for bullet points
                processed_data.append({
                    "title_ar": None,
                    "title_en": None,
                    "subtitle_ar": None,
                    "subtitle_en": None,
                    "descriptions_ar": section_ar.get("bullets", []),
                    "descriptions_en": section_en.get("bullets", []),
                    "type": "bullet points"
                })

        else:
            if 'bullets' in section_en:
                processed_section["descriptions_ar"] = section_ar.get("bullets", [])
                processed_section["descriptions_en"] = section_en.get("bullets", [])
                processed_section["type"] = "bullet points"
                processed_data.append(processed_section)

    return processed_data

# Process the sections
processed_policies = process_sections(policies_data_en, policies_data_ar)

# Print the processed data
print(json.dumps(processed_policies, ensure_ascii=False, indent=4))

# # Save the processed data to a file
with open('d:/Amr/Safarny/Dev ops/Testing/Policies/BlueBus_Processed.json', 'w', encoding='utf-8') as file:
    json.dump(processed_policies, file, ensure_ascii=False, indent=4)

import json
with open('d:/Amr/Safarny/Dev ops/Testing/TermsData_processed.json', 'w', encoding='utf-8') as file:
    json.dump(processed_policies, file, ensure_ascii=False, indent=4)

# loop through the processed data and send it to the API
import requests
accessToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI4NjFjYzA3MS1lYzEzLTRlOTctYTBmZS1lYWU4YjA1YTI1ZjMiLCJyb2xlIjoiYWRtaW4iLCJpYXQiOjE3NTQ5OTg4NjQsImV4cCI6MTc1NTA4NTI2NH0.Zeisgz7NbBrDXIu1qoqw4sNpPCKBYdEZW4nemwXBAm8"
import json
url = "https://prod.tregotech.com/tr/admin/ourPolicy/insertPolicy"

headers = {
    "Content-Type": "application/json",
    "Authorization": f"Bearer {accessToken}"
}

with open('d:/Amr/Safarny/Dev ops/Testing/Policies/policiesData_processed.json', 'r', encoding='utf-8') as file:
    processed_data = json.load(file)

for section in processed_data:
    response = requests.post(url, headers=headers, json=section)
    print(response.json())

# Create an array called new_data
new_data = [{"id": i, "status" : "invisible","index": i-6 } for i in range(0, 120)]
print(new_data)

url = "https://api.tregotech.com/tr/admin/ourPolicy/updateIndex"

for item in new_data:
    response = requests.post(url, headers=headers, json=item)
    print(response)

# make an api call to this https://api.tregotech.com/tr/admin/terms/getTerms
import requests

url = "https://api.tregotech.com/tr/admin/policy/getVisiblePolicy"

response = requests.post(url, {
    "companyName": "blueBus"
})
data = response.json()

print(json.dumps(data, indent=4, ensure_ascii=False))

# Create an array called new_data
new_data = [{"id": i, "status" : "visible","index": i-6 } for i in range(6, 22)]

print(new_data)

# make a put to this https://api.tregotech.com/tr/admin/terms/updateIndex
import requests

url = "https://api.tregotech.com/tr/admin/terms/updateIndex"

# # Create an array called new_data
# new_data = [{"id": i, "status" : "visible","index": i - 14 } for i in range(14, 49)]


headers = {
    "Content-Type": "application/json"
}

# data = {
#     newData :new_data
# }

data = {
    
    "newData": new_data

}

json_data = json.dumps(data)

response = requests.put(url, headers=headers, json=json_data)

print(response)
